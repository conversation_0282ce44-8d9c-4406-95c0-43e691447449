services:
  moer1:
    image: crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/moer_test/moer_test:latest
    container_name: moer1
    environment:
      - NODE_ENV=dev
      - WECHAT_NAME=qiaoqiao
      - TZ=Asia/Shanghai
    ports:
      - "4001:4001"
    restart: always

  moer2:
    image: crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/moer_test/moer_test:latest
    container_name: moer2
    environment:
      - NODE_ENV=dev
      - WECHAT_NAME=tongtong
      - TZ=Asia/Shanghai
    ports:
      - "4002:4002"
    restart: always

  moer3:
    image: crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/moer_test/moer_test:latest
    container_name: moer3
    environment:
      - NODE_ENV=dev
      - WECHAT_NAME=moer3
      - TZ=Asia/Shanghai
    ports:
      - "4004:4004"
    restart: always

  moer4:
    image: crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/moer_test/moer_test:latest
    container_name: moer4
    environment:
      - NODE_ENV=dev
      - WECHAT_NAME=moer4
      - TZ=Asia/Shanghai
    ports:
      - "4005:4005"
    restart: always

  moer5:
    image: crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/moer_test/moer_test:latest
    container_name: moer5
    environment:
      - NODE_ENV=dev
      - WECHAT_NAME=moer5
      - TZ=Asia/Shanghai
    ports:
      - "4006:4006"
    restart: always

  moer6:
    image: crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/moer_test/moer_test:latest
    container_name: moer6
    environment:
      - NODE_ENV=dev
      - WECHAT_NAME=moer6
      - TZ=Asia/Shanghai
    ports:
      - "4007:4007"
    restart: always

  moer7:
    image: crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/moer_test/moer_test:latest
    container_name: moer7
    environment:
      - NODE_ENV=dev
      - WECHAT_NAME=moer7
      - TZ=Asia/Shanghai
    ports:
      - "4008:4008"
    restart: always

  moer9:
    image: crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/moer_test/moer_test:latest
    container_name: moer9
    environment:
      - NODE_ENV=dev
      - WECHAT_NAME=moer9
      - TZ=Asia/Shanghai
    ports:
      - "4010:4010"
    restart: always

  moer10:
    image: crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/moer_test/moer_test:latest
    container_name: moer10
    environment:
      - NODE_ENV=dev
      - WECHAT_NAME=moer10
      - TZ=Asia/Shanghai
    ports:
      - "4011:4011"
    restart: always

  moer11:
    image: crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/moer_test/moer_test:latest
    container_name: moer11
    environment:
      - NODE_ENV=dev
      - WECHAT_NAME=moer11
      - TZ=Asia/Shanghai
    ports:
      - "4012:4012"
    restart: always

  moer12:
    image: crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/moer_test/moer_test:latest
    container_name: moer12
    environment:
      - NODE_ENV=dev
      - WECHAT_NAME=moer12
      - TZ=Asia/Shanghai
    ports:
      - "4013:4013"
    restart: always

  moer13:
    image: crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/moer_test/moer_test:latest
    container_name: moer13
    environment:
      - NODE_ENV=dev
      - WECHAT_NAME=moer13
      - TZ=Asia/Shanghai
    ports:
      - "4014:4014"
    restart: always

  moer15:
    image: crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/moer_test/moer_test:latest
    container_name: moer15
    environment:
      - NODE_ENV=dev
      - WECHAT_NAME=moer15
      - TZ=Asia/Shanghai
    ports:
      - "4016:4016"
    restart: always

  moer18:
    image: crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/moer_test/moer_test:latest
    container_name: moer18
    environment:
    - NODE_ENV=dev
    - WECHAT_NAME=moer18
    - TZ=Asia/Shanghai
    ports:
    - "4019:4019"
    restart: always

  moer19:
    image: crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/moer_test/moer_test:latest
    container_name: moer19
    environment:
      - NODE_ENV=dev
      - WECHAT_NAME=moer19
      - TZ=Asia/Shanghai
    ports:
      - "4020:4020"
    restart: always

  moer20:
    image: crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/moer_test/moer_test:latest
    container_name: moer20
    environment:
      - NODE_ENV=dev
      - WECHAT_NAME=moer20
      - TZ=Asia/Shanghai
    ports:
      - "4021:4021"
    restart: always

  moer21:
    image: crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/moer_test/moer_test:latest
    container_name: moer21
    environment:
      - NODE_ENV=dev
      - WECHAT_NAME=moer21
      - TZ=Asia/Shanghai
    ports:
        - "4022:4022"
    restart: always
#
#   moer22:
#     image: crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/moer_test/moer_test:latest
#     container_name: moer22
#     environment:
#       - NODE_ENV=dev
#       - WECHAT_NAME=moer22
#       - TZ=Asia/Shanghai
#     ports:
#         - "4023:4023"
#     restart: always

  moer22:
    image: crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/moer_test/moer_test:latest
    container_name: moer22
    environment:
      - NODE_ENV=dev
      - WECHAT_NAME=moer22
      - TZ=Asia/Shanghai
    ports:
      - "4024:4024"
    restart: always

  moer23:
    image: crpi-5ht6itmmkqtumodb.cn-hangzhou.personal.cr.aliyuncs.com/moer_test/moer_test:latest
    container_name: moer23
    environment:
      - NODE_ENV=dev
      - WECHAT_NAME=moer23
      - TZ=Asia/Shanghai
    ports:
      - "4025:4025"
    restart: always
