import { ChatHistoryService } from '../bot/service/moer/components/chat_history/chat_history'
import { chatHistoryWithRoleAndDateListToString } from '../bot/service/moer/components/flow/helper/slotsExtract'
import dayjs from 'dayjs'
import { LLM } from '../bot/lib/ai/llm/LLM'
import logger from '../bot/model/logger/logger'
import { DateHelper } from '../bot/lib/date/date'
import { DataService } from '../bot/service/moer/getter/getData'
import { getPrompt } from '../bot/service/moer/components/agent/prompt'
import { UUID } from '../bot/lib/uuid/uuid'
import { taskTimeToCourseTime } from '../bot/service/moer/components/schedule/creat_schedule_task'

// Mock function to calculate course time info based on currentDate
async function mockGetCourseTimeInfo(currentDate: Date, chatId: string): Promise<string> {
  try {
    // Calculate schedule time based on currentDate instead of new Date()
    const currentTime = await taskTimeToCourseTime(currentDate, chatId)

    if (!currentTime.is_course_week) {
      return ''
    }

    const dayEndTimeMap: Record<number, string> = {
      1: '21:35:00', // 星期一
      2: '21:05:00', // 星期二
      3: '21:40:00', // 星期三
      4: '22:05:00'  // 星期四
    }

    const courseName: Record<number, string> = {
      1: '情绪减压', // 星期一
      2: '财富唤醒', // 星期二
      3: '红鞋子飞跃', // 星期三
      4: '蓝鹰预演'  // 星期四
    }

    let todayCourse = ''
    let tomorrowCourse = ''

    if (currentTime.day > 0 && currentTime.day < 4) {
      // Simple mock logic for course status based on time
      const currentHour = currentDate.getHours()
      const currentMinute = currentDate.getMinutes()
      const currentTimeStr = `${currentHour.toString().padStart(2, '0')}:${currentMinute.toString().padStart(2, '0')}:00`

      const dayEndTime = dayEndTimeMap[currentTime.day]
      const isAfterCourse = currentTimeStr > dayEndTime
      const isInCourse = currentHour >= 20 && currentHour < 22 // Assume course is between 20:00-22:00
      const courseStatus = isAfterCourse ? '已结束' : (isInCourse ? '进行中' : '未开始')

      todayCourse = `今日课程：第${currentTime.day}课${courseName[currentTime.day]}，${courseStatus}。`
    }

    if (currentTime.day >= 0 && currentTime.day < 3) {
      tomorrowCourse = `明日课程：第${currentTime.day + 1}课`
    }

    if (currentTime.day === 4) {
      const currentHour = currentDate.getHours()
      if (currentHour < 14) {
        todayCourse = '昨日课程：第3课红靴子，已结束'
      } else {
        const currentTimeStr = `${currentHour.toString().padStart(2, '0')}:${currentDate.getMinutes().toString().padStart(2, '0')}:00`
        const dayEndTime = dayEndTimeMap[currentTime.day]
        const isAfterCourse = currentTimeStr > dayEndTime
        const isInCourse = currentHour >= 20 && currentHour < 22
        const courseStatus = isAfterCourse ? '已结束' : (isInCourse ? '已开始' : '未开始')
        todayCourse = `今日课程：第${currentTime.day}课${courseName[currentTime.day]}，${courseStatus}。`
      }
    }

    return `${todayCourse}${tomorrowCourse}`
  } catch (e) {
    return ''
  }
}

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    // 5 轮一次， 将之前的结果，放到下一次的输入中
    const chat_ids = ['7881300794909689_1688857003605938']
    const roundId = UUID.v4()

    let prevMemory = ''

    for (const chatId of chat_ids) {
      const chat_history =  await ChatHistoryService.getChatHistoryByChatId(chatId)
      let userMessageCount = 0

      let cursor = 0

      for (let i = 0; i < chat_history.length ; i++) {
        if (chat_history[i].role === 'user') {
          userMessageCount += 1
        } else {
          continue
        }

        if (userMessageCount % 5 === 0) {
          // 处理聊天记录
          const chatHistory = chat_history.slice(cursor, i)

          const dialogHistory = chatHistory.map((message) => ({ role:message.role, date:dayjs(message.created_at).format('YYYY/MM/DD HH:mm:ss'), message:message.content }))

          cursor = i + 1

          const chatHistoryStr = chatHistoryWithRoleAndDateListToString(dialogHistory)

          // mock Temporal Info
          const courseStartTime = await DataService.getCourseStartTime(chatId)

          const currentDate = chat_history[i].created_at
          // 以当前时间 和 开课时间，反向计算出时间信息

          const temporalInformation =  `- 当前时间：${DateHelper.getFormattedDate(currentDate, true)}，${DateHelper.getTimeOfDay(currentDate)} ${await mockGetCourseTimeInfo(currentDate, chatId)}
- 上课时间：${DateHelper.getFormattedDate(courseStartTime, false)}开始到周三每晚20:00`

          const recorderPrompt = await getPrompt('recorder')

          // 客户记忆
          const memoryOutput = await LLM.predict(
            recorderPrompt, {
              model: 'gpt-5-mini',
              maxTokens: 4096,
              responseJSON: true,
              meta: {
                promptName: 'recorder',
                chat_id: chatId,
                round_id: roundId
              } }, {
              customerMemory: prevMemory,
              dialogHistory: chatHistoryStr,
              temporalInformation: temporalInformation,
            })

          const parsedOutput = JSON.parse(memoryOutput)
          const portrait = parsedOutput['客户记忆'] || {}

          logger.log(JSON.stringify({
            input: {
              customerMemory: prevMemory,
              dialogHistory: chatHistoryStr,
              temporalInformation: temporalInformation,
            },
            output: portrait,
            modelOutput: parsedOutput
          }, null, 4))

          prevMemory = JSON.stringify(portrait, null, 2)

        }
      }

    }
  }, 1E8)
})