import { PromptTemplate } from '@langchain/core/prompts'
import { LLM } from '../../bot/lib/ai/llm/LLM'
import fs from 'fs'
import * as mammoth from 'mammoth'
import { RecursiveCharacterTextSplitter } from 'langchain/text_splitter'
import { XMLHelper } from '../../bot/lib/xml/xml'
import { FileHelper } from '../../bot/lib/file'


export class QAExtraction {


  public static async extract(filepath: string, resPath: string, docName: string) {
    const resList: any[] = []

    const chunks = await this.segmentDoc(filepath)
    for (const chunk of chunks) {
      const res = await this.getLLMRes(chunk)
      console.log('chunk', chunk)
      console.log('res', res)
      const questions = XMLHelper.extractContents(res, 'question')
      const answers = XMLHelper.extractContents(res, 'answer')
      resList.push(this.structureOutput(questions, answers, chunk, docName))
    }

    await FileHelper.writeFile(resPath, JSON.stringify(resList))
  }

  private static structureOutput(questions: string[] | null, answers: string[] | null, chunk: string, docName: string) {
    if (questions === null || answers === null || questions.length != answers.length) {
      console.warn('Invalid question/answer pair:', questions, answers)
      return {
        chunk: chunk,
        qas: [{
          'q': '',
          'a': ''
        }],
        doc: docName,
      }
    }

    return {
      chunk: chunk,
      qas: questions.map((question, index) => ({
        'q': question,
        'a': answers[index]
      })),
      doc: docName,
    }


  }

  private static async segmentDoc(filepath: string) {
    if (!fs.existsSync(filepath)) {
      throw new Error(`File not found: ${filepath}`)
    }

    const extension = filepath.split('.').pop()?.toLowerCase()
    let content = ''

    try {
      if (extension === 'docx') {
        const dataBuffer = fs.readFileSync(filepath)
        const result = await mammoth.extractRawText({ buffer: dataBuffer })
        content = result.value
      } else if (extension === 'doc') {
        const dataBuffer = fs.readFileSync(filepath)
        const result = await mammoth.extractRawText({ buffer: dataBuffer })
        content = result.value
      } else {
        const fileStream = fs.readFileSync(filepath, 'utf-8')
        content = fileStream.toString()
      }
    } catch (fileError) {
      throw new Error(`Failed to read file: ${fileError}`)
    }

    try {
      const chunks = await this.recursiveTextSplitter(content, 2000, 500)
      return chunks.filter((chunk) => chunk.trim().length > 0)
    } catch (apiError) {
      throw new Error(`Failed to split document ${apiError}`)
    }



  }

  private static recursiveTextSplitter(text: string, max_length: number, overlap: number): Promise<string[]> {
    const splitter = new RecursiveCharacterTextSplitter({
      chunkSize: max_length,
      chunkOverlap: overlap,
      keepSeparator: true
    })
    return splitter.splitText(text)
  }


  private static async getLLMRes(document: string) {
    const promptTemplate = PromptTemplate.fromTemplate(`# 背景
这段内容是一个关于抖音营销和AI获客的直播培训课程最后一天的记录。中神通老师分享了"抖音红利机会"和"搞钱"方法，介绍了抖音每年推出的不同变现机会，从2017-2018年的娱乐时代，到2019年的影视剪辑，2021年的三农内容，到现在2025年的本地生活和AI获客引流。讲师强调了"跟对趋势"的重要性，指出抖音正大力扶持本地生活市场以与美团竞争，提出"AI获客流量师"将是一个风靡的新职业。课程中提到了多种变现方式，包括收取服务费、销售软件(数字人、AI矩阵系统、爆店码、无人直播系统)和收取销售佣金。最后推出了陪跑服务，包含"六对一陪跑"、"532打法"(流量内容、人设内容、产品内容的配比)、"矩阵爆店"等策略，以及如何打造不同类型的人设(专家型、真诚型、实力型)和制作爆款视频。

# 任务
请分析给定的文档块，并生成相关的中文问答对，给到的文档块的内容与线上营销相关。你需要根据文档内容你与学员的关系（你是课程的助教老师，学员是各行业的实体店老板），从文档中提取可能的问答对。

# 文档块内容
{document}

# 要求：
1. 要求问题简洁易懂
2. 确保每个 XML 标记都正确闭合。
3. 如果无法生成有意义的问答对，请跳过这部分内容。
4. 确保答案包含有效信息，避免在问题或答案中提及 "相关文件"，"相关文本"，"相关渠道"或者"文档"等词汇。
5. 问题所属行业只有一个
6. 确保答案详细、完整，并准确反映源内容的中文描述（如适用，请使用文档中的原始内容）。

# 输出格式
<query>
  <question>客户问题</question>
  <answer>专业老师的回答</answer>
  <tag>问题所属行业（例如：餐饮、装修建材、服装、美业、综合行业）</tag>
</query>

`)


    return await LLM.predict(
      promptTemplate,
      { model: 'gpt-5' },
      { document }
    )
  }
}

