import { ChatHistoryService, IDBBaseMessage } from '../../bot/service/moer/components/chat_history/chat_history'
import { Config } from '../../bot/config/config'
import { Client, Run } from 'langsmith'
import { XMLHelper } from '../../bot/lib/xml/xml'
import { PrismaMongoClient } from '../../bot/model/mongodb/prisma'


export class ChatHistoryTool {

  public static async getChatHistory(chatId: string, chatHistoryId: string, rounds: number = 5, limitLength: number = 10) {
    const chatHistories = await ChatHistoryService.getChatHistoryByChatId(chatId, true)

    const index = chatHistories.findIndex((item) => item.id === chatHistoryId)
    const sliceHistories = chatHistories.slice(0, index + 1)

    let roundCount = 0
    let cleanChatHistory: IDBBaseMessage[] = []

    for (let i = sliceHistories.length - 1; i >= 0; i--) {
      // 如果是客户消息，增加轮次计数
      if (sliceHistories [i].role === 'user') {
        roundCount++
      }
      // 如果达到指定轮次数，结束遍历
      if (roundCount === rounds || i === 0) {
        cleanChatHistory = sliceHistories.slice (i, sliceHistories.length)
        break
      }
    }

    return cleanChatHistory.slice(-limitLength)
  }

  public static async getChatHistoryByMsg(chatId: string, message: string, rounds: number = 5, limitLength: number = 30) {
    const chatHistories = await ChatHistoryService.getChatHistoryByChatId(chatId)

    const index = chatHistories.findIndex((item) => message.includes(item.content) && (item.content.length > 5 || message.length < 5))

    return await ChatHistoryTool.getChatHistory(chatId, chatHistories[index].id, rounds, limitLength)
  }

  public static async getChatStateByAiMsg(chatId: string, message: string) {
    const chatHistories = await ChatHistoryService.getChatHistoryByChatId(chatId)

    const index = chatHistories.findIndex((item) => message.includes(item.content) && (item.content.length > 5 || message.length < 5))

    const chatHistory = await PrismaMongoClient.getInstance().chat_history.findFirst({
      where: {
        id: chatHistories[index].id
      }
    })

    return  chatHistory?.chat_state
  }

  public static async getChatStateByUserMsg(chatId: string, message: string) {
    const chatHistories = await ChatHistoryService.getChatHistoryByChatId(chatId)

    const index = chatHistories.findIndex((item) => message.includes(item.content) && (item.content.length > 5 || message.length < 5))
    let aiMsgIndex = 0


    for (let i = index;i < chatHistories.length;i++) {
      if (chatHistories[i].role === 'assistant') {
        aiMsgIndex = i
        break
      }
    }

    if (aiMsgIndex > 0) {
      const chatHistory = await PrismaMongoClient.getInstance().chat_history.findFirst({
        where: {
          id: chatHistories[aiMsgIndex].id
        }
      })
      return chatHistory?.chat_state
    }

    return undefined
  }

  public static async getAiMsgByUserMsg(chatId: string, message: string) {
    const chatHistories = await ChatHistoryService.getChatHistoryByChatId(chatId)


    const index = chatHistories.findIndex((item) => message.includes(item.content) && (item.content.length > 5 || message.length < 5))
    let aiMsg = ''
    let shouldSkip = true
    let roundId: string | undefined = undefined

    for (let i = index;i < chatHistories.length;i++) {
      if (shouldSkip &&  chatHistories[i].role === 'user') {
        continue
      }
      if (chatHistories[i].role === 'assistant' && (!roundId || roundId === chatHistories[i].round_id)) {
        shouldSkip = false
        roundId = chatHistories[i].round_id
        aiMsg += chatHistories[i].content
      }
      else break
    }

    return aiMsg
  }

  public static async getChatHistoryByUserMsg(chatId: string, message: string) {
    const chatHistories = await ChatHistoryService.getChatHistoryByChatId(chatId)
    const index = chatHistories.findIndex((item) => message.includes(item.content) && (item.content.length > 5 || message.length < 5))
    for (let i = index;i < chatHistories.length;i++) {
      if (chatHistories[i].role === 'assistant') {
        return chatHistories[i]
      }
    }
    return undefined
  }

  public static async getTraceFromLangSmith(roundId: string) {
    process.env.LANGCHAIN_API_KEY = Config.setting.langsmith.apiKey
    process.env.LANGCHAIN_TRACING_V2 = 'true'
    process.env.LANGCHAIN_PROJECT = Config.setting.langsmith.projectName
    const params = {
      roundIdKey: 'round_id',
      roundId: roundId,
    }
    const client = new Client()
    // const runs = client.listRuns({
    //   projectName: 'moer',
    //   filter: `and (eq (metadata_key, '${params.roundIdKey}'), eq (metadata_value, '${params.roundId}'))`,
    //   error: false,
    // })

    const todaysLlmRuns: Run[] = []
    for await (const run of client.listRuns({
      projectName: 'moer',
      startTime: new Date(Date.now() - 60 * 60 * 30),
      runType: 'llm',
    })) {
      todaysLlmRuns.push(run)
    }

    console.log(JSON.stringify(todaysLlmRuns, null, 4))

    // let output = ''
    // for await (const run of runs) {
    //   if (
    //     run.name === 'StrOutputParser' &&
    //       (run.extra?.metadata.promptName === 'sales_strategy' || run.extra?.metadata.promptName === 'think_strategy') &&
    //       run.outputs?.output
    //   ) {
    //     output = run.outputs.output
    //   }
    // }
    //
    // const think = XMLHelper.extractContent(output, 'think')
    // const strategy = XMLHelper.extractContent(output, 'strategy')
    //
    // return { think, strategy }
  }



}