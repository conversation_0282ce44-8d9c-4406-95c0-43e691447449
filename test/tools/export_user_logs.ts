import { PrismaMongoClient } from '../../bot/model/mongodb/prisma'
import { queryChats } from '../../admin_platform/app/api/chat'
import { queryLogByChatId } from '../../admin_platform/app/api/log_store'
import * as fs from 'fs'
import * as path from 'path'

interface UserLogData {
  userName: string
  chatId: string | null
  logs: any[]
}

/**
 * 导出指定客户的日志工具
 */
export class UserLogExporter {

  // 要查询的客户名列表
  private static readonly TARGET_USERS = [
    '覃雨祁（字菲凡）',
    '黄老师筋膜学15810434116',
    'F82似是故人来（李艳13',
    '易手遮天',
    '路美华'
  ]

  /**
   * 根据客户名查找chat_id
   */
  private static async findChatIdByUserName(userName: string): Promise<string | null> {
    try {
      console.log(`正在查找客户: ${userName}`)

      // 使用queryChats函数搜索客户
      const users = await queryChats(userName)

      if (users.length === 0) {
        console.log(`未找到客户: ${userName}`)
        return null
      }

      // 如果找到多个客户，选择最后一个
      if (users.length > 1) {
        console.log(`找到多个匹配客户 (${users.length}个)，选择第一个: ${users[0].contact.wx_name}`)
      } else {
        console.log(`找到客户: ${users[0].contact.wx_name}`)
      }

      return users[0].id
    } catch (error) {
      console.error(`查找客户 ${userName} 时出错:`, error)
      return null
    }
  }

  /**
   * 获取客户的所有日志
   */
  private static async getUserLogs(chatId: string): Promise<any[]> {
    try {
      const logs = await queryLogByChatId(chatId)
      console.log(`获取到 ${logs.length} 条日志`)
      return logs
    } catch (error) {
      console.error(`获取日志时出错 (chatId: ${chatId}):`, error)
      return []
    }
  }

  /**
   * 将日志写入文件
   */
  private static async writeLogsToFile(userName: string, logs: any[]): Promise<void> {
    try {
      // 创建输出目录
      const outputDir = path.join(__dirname, 'user_logs')
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true })
      }

      // 按时间排序（从早到晚）
      const sortedLogs = logs.sort((a, b) =>
        new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
      )

      // 格式化日志内容
      const logContent = sortedLogs.map((log) => {
        const timestamp = new Date(log.timestamp).toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        })
        return `[${timestamp}] [${log.level.toUpperCase()}] ${log.msg}`
      }).join('\n')

      // 添加文件头信息
      const fileHeader = `客户日志导出
客户名: ${userName}
日志条数: ${sortedLogs.length}
导出时间: ${new Date().toLocaleString('zh-CN')}
时间范围: ${sortedLogs.length > 0 ?
    `${new Date(sortedLogs[0].timestamp).toLocaleString('zh-CN')} 至 ${new Date(sortedLogs[sortedLogs.length - 1].timestamp).toLocaleString('zh-CN')}` :
    '无日志'
}

${'='.repeat(80)}

`

      const finalContent = fileHeader + logContent

      // 生成安全的文件名
      const safeFileName = userName.replace(/[<>:"/\\|?*]/g, '_')
      const filePath = path.join(outputDir, `${safeFileName}_logs.txt`)

      fs.writeFileSync(filePath, finalContent, 'utf8')
      console.log(`日志已写入文件: ${filePath}`)

    } catch (error) {
      console.error(`写入文件时出错 (客户: ${userName}):`, error)
    }
  }

  /**
   * 导出所有目标客户的日志
   */
  public static async exportAllUserLogs(): Promise<void> {
    console.log('开始导出客户日志...')
    console.log(`目标客户: ${this.TARGET_USERS.join(', ')}`)
    console.log('='.repeat(80))

    const results: UserLogData[] = []

    for (const userName of this.TARGET_USERS) {
      console.log(`\n处理客户: ${userName}`)

      // 查找chat_id
      const chatId = await this.findChatIdByUserName(userName)

      if (!chatId) {
        results.push({
          userName,
          chatId: null,
          logs: []
        })
        continue
      }

      // 获取日志
      const logs = await this.getUserLogs(chatId)

      // 写入文件
      if (logs.length > 0) {
        await this.writeLogsToFile(userName, logs)
      } else {
        console.log(`客户 ${userName} 没有日志记录`)
      }

      results.push({
        userName,
        chatId,
        logs
      })
    }

    // 输出汇总信息
    console.log(`\n${  '='.repeat(80)}`)
    console.log('导出完成！汇总信息:')
    results.forEach((result) => {
      console.log(`${result.userName}: ${result.chatId ? `${result.logs.length} 条日志` : '未找到客户'}`)
    })

    console.log(`\n日志文件保存在: ${path.join(__dirname, 'user_logs')}`)
  }
}

// 如果直接运行此文件，则执行导出
if (require.main === module) {
  UserLogExporter.exportAllUserLogs()
    .then(() => {
      console.log('脚本执行完成')
      process.exit(0)
    })
    .catch((error) => {
      console.error('脚本执行失败:', error)
      process.exit(1)
    })
}
