import { Config } from '../bot/config/config'
import { Client, Run } from 'langsmith'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    process.env.LANGCHAIN_API_KEY = Config.setting.langsmith.apiKey
    // process.env.LANGCHAIN_TRACING_V2 = 'true'
    process.env.LANGCHAIN_PROJECT = Config.setting.langsmith.projectName
    const client = new Client()

    // Check for runs with user_id=4070f233-f61e-44eb-bff1-da3c163895a3
    const runs = client.listRuns({
      projectName: 'moer',
      filter: 'and(eq(metadata_key, \'round_id\'), eq(metadata_value, \'hohCzumVbs5t7so7ybFpbC\'))',
    })

    for await (const run of runs) {
      console.log(JSON.stringify(run, null, 4))
    }
  }, 60000)

  it('213213213', async () => {
    process.env.LANGCHAIN_API_KEY = Config.setting.langsmith.apiKey
    // process.env.LANGCHAIN_TRACING_V2 = 'true'
    process.env.LANGCHAIN_PROJECT = Config.setting.langsmith.projectName
    const client = new Client()

    // chat_id
    const runs = client.listRuns({
      projectName: 'moer',
      filter: 'and(eq(metadata_key, \'promptName\'), eq(metadata_value, \'freeBigPlan\'), eq(metadata_key, \'chat_id\'), eq(metadata_value, \'7881302159168001_1688857003605938\'))'
    })

    const res: Run[] = []

    for await (const run of runs) {
      if (run.run_type === 'prompt') {
        res.push(run)
        console.log(JSON.stringify(run, null, 4))
      }
    }

    console.log(res.length)


    // 拉取 对应的 chat_id 下的 Planner run 的输入， 进行调试

    // 作为 Planner input

    // 每个 Planner 最后生成的任务
    // add， update 中的任务整理下时间顺序，然后分别去执行 （执行的时间点，可能要 Mock 一下时间）


  }, 30000)

  it('567890-p=', async () => {
    process.env.LANGCHAIN_API_KEY = Config.setting.langsmith.apiKey
    process.env.LANGCHAIN_PROJECT = Config.setting.langsmith.projectName

    const chatId = '7881302159168001_1688857003605938'
    const client = new Client()

    const runs = client.listRuns({
      projectName: 'moer',
      filter: `and(eq(metadata_key, 'promptName'), eq(metadata_value, 'freeBigPlan'), eq(metadata_key, 'chat_id'), eq(metadata_value, '${chatId}'))`
    })

    const results: Run[] = []

    for await (const run of runs) {
      if (run.run_type === 'prompt' && run.inputs) {
        results.push(run)
      }
    }

    // 按时间排序，取最新的6条（对应6天）
    const res = results
      .sort((a, b) => new Date(b.start_time as string).getTime() - new Date(a.start_time as string).getTime())
      .slice(0, 6)

    console.log(JSON.stringify(res, null, 4))
  }, 30000)
})