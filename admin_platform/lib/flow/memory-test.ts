import dayjs from 'dayjs'
import { randomUUID } from 'crypto'
import { getPrompt } from '../../../bot/service/moer/components/agent/prompt'
import { ContextBuilder } from '../../../bot/service/moer/components/agent/context'
import { ChatHistoryService, type IDBBaseMessage } from '../../../bot/service/moer/components/chat_history/chat_history'
import { ChatStatStoreManager, ChatStateStore } from '../../../bot/service/moer/storage/chat_state_store'
import { chatHistoryWithRoleAndDateListToString, type ChatHistoryWithRoleAndDate } from '../../../bot/service/moer/components/flow/helper/slotsExtract'
import { LLM } from '../../../bot/lib/ai/llm/LLM'
import type { ExecutionStep, ExecutionTrace } from '@/types/flow'

export interface MemoryTestParams {
  chatId: string
  customerMemory?: string
  startDate?: string
  endDate?: string
  roundId?: string
  promptOverride?: string
}

interface MemoryTestResult {
  trace: ExecutionTrace
  memoryTestOutput: Record<string, unknown>
  rawOutput: string
  chatHistory: ChatHistoryWithRoleAndDate[]
  roundId: string
}

function mapChatHistory(records: IDBBaseMessage[], start?: string, end?: string): ChatHistoryWithRoleAndDate[] {
  const startDate = start ? dayjs(start).startOf('day') : null
  const endDate = end ? dayjs(end).endOf('day') : null

  return records
    .filter((item) => {
      if (!startDate && !endDate) return true
      const created = dayjs(item.created_at)
      if (startDate && created.isBefore(startDate)) return false
      if (endDate && created.isAfter(endDate)) return false
      return true
    })
    .map((item) => ({
      role: item.role,
      date: dayjs(item.created_at).format('YYYY/MM/DD HH:mm:ss'),
      message: item.content,
    }))
}

export async function runMemoryTestFlow(params: MemoryTestParams): Promise<MemoryTestResult> {
  const { chatId, customerMemory = '', startDate, endDate, promptOverride } = params
  const roundId = params.roundId ?? randomUUID()

  if (!chatId) {
    throw new Error('chatId 不能为空')
  }

  await ChatStatStoreManager.initState(chatId)
  const trace: ExecutionTrace = []

  // Step 1: 输入参数
  trace.push({
    nodeId: 'memory-test-mode',
    nodeType: 'modeNode',
    inputData: null,
    outputData: {
      chatId,
      customerMemory,
      startDate,
      endDate,
      roundId,
    },
  })

  // Step 2: 获取聊天记录
  const historyRecords = await ChatHistoryService.getChatHistoryByChatId(chatId, true)
  const filteredHistory = mapChatHistory(historyRecords, startDate, endDate)

  trace.push({
    nodeId: 'memory-test-input',
    nodeType: 'inputNode',
    inputData: {
      historyCount: historyRecords.length,
      customerMemory,
    },
    outputData: filteredHistory,
  })

  const memoryTestPrompt = promptOverride ?? (await getPrompt('recorder', true))
  const dialogHistory = chatHistoryWithRoleAndDateListToString(filteredHistory)
  const temporalInformation = await ContextBuilder.temporalInformation(chatId)

  let rawOutput = ''
  let parsedMemoryTest: Record<string, unknown> = {}
  const memoryTestStep: ExecutionStep = {
    nodeId: 'memory-test-prompt',
    nodeType: 'promptNode',
    inputData: {
      promptName: promptOverride ? 'custom-memory-test-prompt' : 'recorder',
      temporalInformation,
      dialogHistorySample: dialogHistory,
      customerMemory,
    },
    outputData: null,
  }

  try {
    rawOutput = await LLM.predict(
      memoryTestPrompt,
      {
        model: 'gpt-5-mini',
        maxTokens: 9999,
        responseJSON: true,
        meta: {
          promptName: 'memory_test',
          chat_id: chatId,
          round_id: roundId
        },
        projectName: 'evaluators'
      },
      {
        customerMemory,
        dialogHistory,
        temporalInformation,
      },
    )

    try {
      const parsedOutput = JSON.parse(rawOutput)
      const memoryTest = parsedOutput['记忆测试'] ?? parsedOutput
      parsedMemoryTest = memoryTest ?? {}
    } catch {
      parsedMemoryTest = {}
    }

    memoryTestStep.outputData = {
      rawOutput,
      parsedMemoryTest,
    }
  } catch (error) {
    memoryTestStep.error = error instanceof Error ? error.message : String(error)
    memoryTestStep.outputData = {
      rawOutput,
      parsedMemoryTest,
    }
  }

  trace.push(memoryTestStep)

  trace.push({
    nodeId: 'memory-test-output',
    nodeType: 'outputNode',
    inputData: parsedMemoryTest,
    outputData: parsedMemoryTest,
  })

  return {
    trace,
    memoryTestOutput: parsedMemoryTest,
    rawOutput,
    chatHistory: filteredHistory,
    roundId,
  }
}
