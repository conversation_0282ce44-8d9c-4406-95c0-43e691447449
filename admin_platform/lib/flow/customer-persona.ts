import dayjs from 'dayjs'
import { randomUUID } from 'crypto'
import { getPrompt } from '../../../bot/service/moer/components/agent/prompt'
import { ContextBuilder } from '../../../bot/service/moer/components/agent/context'
import { ChatHistoryService, type IDBBaseMessage } from '../../../bot/service/moer/components/chat_history/chat_history'
import { ChatStateStore, ChatStatStoreManager } from '../../../bot/service/moer/storage/chat_state_store'
import {
  type ChatHistoryWithRoleAndDate,
  chatHistoryWithRoleAndDateListToString,
  CustomerPortrait
} from '../../../bot/service/moer/components/flow/helper/slotsExtract'
import { LLM } from '../../../bot/lib/ai/llm/LLM'
import type { ExecutionStep, ExecutionTrace } from '@/types/flow'

export interface CustomerPersonaParams {
  chatId: string
  startDate?: string
  endDate?: string
  isDanmu?: boolean
  roundId?: string
  promptOverride?: string
  loadExistingPortrait?: boolean
  loadExistingMemory?: boolean
}

interface PersonaResult {
  trace: ExecutionTrace
  portrait: Record<string, unknown>
  rawOutput: string
  chatHistory: ChatHistoryWithRoleAndDate[]
  roundId: string
}

function mapChatHistory(records: IDBBaseMessage[], start?: string, end?: string): ChatHistoryWithRoleAndDate[] {
  const startDate = start ? dayjs(start).startOf('day') : null
  const endDate = end ? dayjs(end).endOf('day') : null

  return records
    .filter((item) => {
      if (!startDate && !endDate) return true
      const created = dayjs(item.created_at)
      if (startDate && created.isBefore(startDate)) return false
      return !(endDate && created.isAfter(endDate))
    })
    .map((item) => ({
      role: item.role,
      date: dayjs(item.created_at).format('YYYY/MM/DD HH:mm:ss'),
      message: item.content,
    }))
}

export async function runCustomerPersonaFlow(params: CustomerPersonaParams): Promise<PersonaResult> {
  const { chatId, startDate, endDate, isDanmu = false, promptOverride, loadExistingPortrait, loadExistingMemory = false } = params
  const roundId = params.roundId ?? randomUUID()
  if (!chatId) { throw new Error('chatId 不能为空') }

  await ChatStatStoreManager.initState(chatId)
  const chatState = ChatStateStore.get(chatId)
  const initialPortrait = loadExistingPortrait ? (CustomerPortrait.getCustomSlotByIsSecret(chatState, isDanmu) ?? {}) : {}

  const trace: ExecutionTrace = []

  // Step 1: 输入参数
  trace.push({
    nodeId: 'persona-mode',
    nodeType: 'modeNode',
    inputData: null,
    outputData: {
      chatId,
      startDate,
      endDate,
      isDanmu,
      roundId,
    },
  })

  // Step 2: 获取聊天记录
  const historyRecords = await ChatHistoryService.getChatHistoryByChatId(chatId, true)
  const filteredHistory = mapChatHistory(historyRecords, startDate, endDate)

  trace.push({
    nodeId: 'persona-input',
    nodeType: 'inputNode',
    inputData: {
      historyCount: historyRecords.length,
    },
    outputData: filteredHistory,
  })

  const painterPrompt = promptOverride ?? (await getPrompt('customer_profile', true))
  const dialogHistory = chatHistoryWithRoleAndDateListToString(filteredHistory)
  const temporalInformation = await ContextBuilder.temporalInformation(chatId)

  let rawOutput = ''
  let parsedPortrait: Record<string, unknown> = {}
  const personaStep: ExecutionStep = {
    nodeId: 'persona-prompt',
    nodeType: 'promptNode',
    inputData: {
      promptName: promptOverride ? 'custom-persona-prompt' : 'painter',
      temporalInformation,
      dialogHistorySample: dialogHistory.slice(0, 4000),
      initialPortrait,
    },
    outputData: null,
  }

  try {
    rawOutput = await LLM.predict(
      painterPrompt,
      {
        model: 'gpt-5-mini',
        maxTokens: 4096,
        responseJSON: true,
        meta: {
          promptName: 'painter',
          chat_id: chatId,
          round_id: roundId
        },
        projectName: 'evaluators'
      },
      {
        customerPortrait: JSON.stringify(initialPortrait),
        dialogHistory,
        temporalInformation,
      },
    )

    try {
      const parsedOutput = JSON.parse(rawOutput)
      const portrait = parsedOutput['客户画像'] ?? parsedOutput
      parsedPortrait = portrait ?? {}
    } catch {
      parsedPortrait = {}
    }

    personaStep.outputData = {
      rawOutput,
      parsedPortrait,
    }
  } catch (error) {
    personaStep.error = error instanceof Error ? error.message : String(error)
    personaStep.outputData = {
      rawOutput,
      parsedPortrait,
    }
  }

  trace.push(personaStep)

  trace.push({
    nodeId: 'persona-output',
    nodeType: 'outputNode',
    inputData: parsedPortrait,
    outputData: parsedPortrait,
  })

  return {
    trace,
    portrait: parsedPortrait,
    rawOutput,
    chatHistory: filteredHistory,
    roundId,
  }
}
