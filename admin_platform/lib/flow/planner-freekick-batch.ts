import { Client, Run } from 'langsmith'
import { v4 as uuidv4 } from 'uuid'
import { JSONHelper } from '../../../bot/lib/json/json'
import logger from '../../../bot/model/logger/logger'
import { ContextBuilder } from '../../../bot/service/moer/components/agent/context'
import { KnowledgeRag } from '../../../bot/service/moer/components/rag/planner/knowledge_rag/knowledge_rag'
import { PlannerMaterialRag } from '../../../bot/service/moer/components/rag/planner/material_rag'
import { getPrompt } from '../../../bot/service/moer/components/agent/prompt'
import { LLM } from '../../../bot/lib/ai/llm/LLM'
import { ChatHistoryService } from '../../../bot/service/moer/components/chat_history/chat_history'
import { ChatStateStore, ChatStatStoreManager } from '../../../bot/service/moer/storage/chat_state_store'
import { Config } from '../../../bot/config/config'
import { UUID } from '../../../bot/lib/uuid/uuid'

export interface PlannerFreeKickBatchParams {
  chatId: string
  selectedDays: string[]
  mainTask?: string
  roundId?: string
}

export interface PlanResponse {
  think: string
  plans: {
    toAdd?: Array<{
      content: string
      send_time: string
      type: string
    }>
    toUpdate?: Array<{
      id: string
      content: string
    }>
    toRemove?: string[]
    toMerge?: Array<{
      from: string[]
      into: string
      mergedContent: string
    }>
  }
}

export interface ExistingTask {
  id: string
  description: string
  time: string
}

export interface FreeKickOutput {
  think: string
  activate: boolean
  action: string[]
  material: string[]
  content: string
}

export interface LangSmithRun {
  id: string
  name: string
  inputs: {
    user_slots: string
    memory: string
    user_behavior: string
    silent_analyze: string
    current_time: string
    existing_task: string
  }
  outputs: any
  start_time: string
  end_time: string
  extra: {
    metadata: {
      chat_id: string
      promptName: string
      thread_id: string
    }
  }
}

export interface PlannerTask {
  id: string
  description: string
  time: string
}

export interface PlannerOutput {
  think: string
  plans: {
    toAdd?: Array<{
      content: string
      send_time: string
      type: string
    }>
    toUpdate?: Array<{
      id: string
      content: string
    }>
    toRemove?: string[]
    toMerge?: Array<{
      from: string[]
      into: string
      mergedContent: string
    }>
  }
}

export interface BatchResult {
  chatId: string
  day: string
  roundId: string
  plannerInput: any
  plannerOutput: PlannerOutput | null
  freeKickResults: Array<{
    taskId: string
    taskDescription: string
    executionTime: string
    input: any
    output: any
    error?: string
  }>
  error?: string
}

/**
 * 真实调用 Planner
 */
export async function callRealPlanner(plannerInput: any): Promise<PlanResponse> {
  try {
    const { chatId } = plannerInput

    // 获取 Planner prompt
    const prompt = await getPrompt('free-big-plan', true)

    // 构建上下文 - 所有字段必须是字符串格式
    const context = {
      user_slots: typeof plannerInput.userSlots === 'string' ? plannerInput.userSlots : JSON.stringify(plannerInput.userSlots || {}),
      memory: typeof plannerInput.memory === 'string' ? plannerInput.memory : JSON.stringify(plannerInput.memory || {}),
      user_behavior: typeof plannerInput.userBehavior === 'string' ? plannerInput.userBehavior : JSON.stringify(plannerInput.userBehavior || {}),
      silent_analyze: typeof plannerInput.silentAnalyze === 'string' ? plannerInput.silentAnalyze : JSON.stringify(plannerInput.silentAnalyze || {}),
      current_time: typeof plannerInput.currentTime === 'string' ? plannerInput.currentTime : String(plannerInput.currentTime || ''),
      existing_task: typeof plannerInput.existingTask === 'string' ? plannerInput.existingTask : JSON.stringify(plannerInput.existingTask || [])
    }

    logger.log({ chat_id: chatId }, `Planner 输入: ${JSON.stringify(context, null, 2)}`)

    // 调用 LLM
    const result = await LLM.predict(
      prompt,
      {
        model: 'gpt-5',
        maxTokens: 9999,
        responseJSON: true,
        promptName: 'freeBigPlan',
        meta: { chat_id: chatId, round_id: plannerInput.roundId },
        projectName: 'evaluators'
      },
      context
    )

    // 解析 LLM 返回的结果
    let planResponse: PlanResponse
    try {
      planResponse = JSONHelper.parse(result) as PlanResponse
      if (!planResponse || !planResponse.plans) {
        throw new Error('Invalid plan response structure')
      }
    } catch (error) {
      logger.error({ chat_id: chatId }, 'BigPlanner 解析 JSON 失败:', error)
      throw error
    }

    logger.log({ chat_id: chatId }, `Planner 思考过程: ${planResponse.think}`)
    logger.log({ chat_id: chatId }, `生成任务: ${JSON.stringify(planResponse.plans, null, 4)}`)

    return planResponse
  } catch (error) {
    console.error('Planner 调用失败:', error)
    throw error
  }
}

/**
 * 真实调用 FreeKick
 */
export async function callRealFreeKick(freeKickInput: any): Promise<FreeKickOutput> {
  try {
    const { chatId, mainTask, roundId } = freeKickInput

    // 获取 FreeKick prompt
    const freeKickPrompt = await getPrompt('free-kick', true)

    // 构建 FreeKick 输入 - 所有字段必须是字符串格式
    const context = {
      courseConfig: typeof freeKickInput.courseConfig === 'string' ? freeKickInput.courseConfig : JSON.stringify(freeKickInput.courseConfig || {}),
      metaActions: typeof freeKickInput.metaActions === 'string' ? freeKickInput.metaActions : String(freeKickInput.metaActions || ''),
      availableMaterials: typeof freeKickInput.availableMaterials === 'string' ? freeKickInput.availableMaterials : String(freeKickInput.availableMaterials || ''),
      retrievedKnowledge: typeof freeKickInput.retrievedKnowledge === 'string' ? freeKickInput.retrievedKnowledge : String(freeKickInput.retrievedKnowledge || ''),
      customerBehavior: typeof freeKickInput.customerBehavior === 'string' ? freeKickInput.customerBehavior : JSON.stringify(freeKickInput.customerBehavior || {}),
      customerPortrait: typeof freeKickInput.customerPortrait === 'string' ? freeKickInput.customerPortrait : JSON.stringify(freeKickInput.customerPortrait || {}),
      dialogHistory: typeof freeKickInput.dialogHistory === 'string' ? freeKickInput.dialogHistory : JSON.stringify(freeKickInput.dialogHistory || []),
      temporalInformation: typeof freeKickInput.temporalInformation === 'string' ? freeKickInput.temporalInformation : JSON.stringify(freeKickInput.temporalInformation || {}),
      mainTask: typeof mainTask === 'string' ? mainTask : String(mainTask || ''),
    }

    // 调用 LLM
    const output = await LLM.predict(
      freeKickPrompt,
      {
        responseJSON: true,
        meta: {
          promptName: 'free_kick',
          chat_id: chatId,
          round_id: roundId,
        },
        maxTokens: 9999,
        projectName: 'evaluators'
      },
      context
    )

    // 解析输出
    let think: string = ''
    let activate: boolean = false
    let action: string[] = []
    let material: string[] = []
    let content: string = ''

    try {
      const parsedOutput = JSON.parse(output)
      think = parsedOutput.think
      activate = parsedOutput.activate
      action = parsedOutput.action
      material = parsedOutput.material
      content = parsedOutput.content
    } catch (error) {
      logger.error('FreeKick 解析 JSON 失败:', error)
      throw error
    }

    logger.debug({ chat_id: chatId, round_id: roundId }, `FreeKick think: ${think}
activate: ${activate}
action: ${JSON.stringify(action)}
material: ${JSON.stringify(material)}
content: ${content}`)

    return {
      think,
      activate,
      action,
      material,
      content
    }
  } catch (error) {
    console.error('FreeKick 调用失败:', error)
    throw error
  }
}

/**
 * 从 LangSmith 拉取指定 chat_id 的 Planner 输入数据
 */
export async function fetchPlannerInputsFromLangSmith(chatId: string): Promise<Run[]> {
  process.env.LANGCHAIN_API_KEY = Config.setting.langsmith.apiKey
  process.env.LANGCHAIN_PROJECT = Config.setting.langsmith.projectName

  const client = new Client()

  console.log('chat_id: ', chatId)

  const runs = client.listRuns({
    projectName: 'moer',
    filter: `and(eq(metadata_key, 'promptName'), eq(metadata_value, 'freeBigPlan'), eq(metadata_key, 'chat_id'), eq(metadata_value, '${chatId}'))`
  })

  const results: Run[] = []

  for await (const run of runs) {
    if (run.run_type === 'prompt' && run.inputs) {
      results.push(run)
    }
  }

  // 按时间正序排序，取最早的6条（对应 day1-day6）
  return results
    .sort((a, b) => new Date(a.start_time as string).getTime() - new Date(b.start_time as string).getTime())
    .slice(0, 6)
}

/**
 * 解析任务执行时间
 */
export function parseTaskExecutionTime(
  task: any,
  existingTasks: ExistingTask[],
  baseDate: string
): string {
  if (task.send_time) {
    // 如果是完整日期时间（以 YYYY-MM-DD 开头）
    if (/^\d{4}-\d{2}-\d{2}/.test(task.send_time)) {
      return task.send_time // 直接用原值
    }

    // 否则拼接 baseDate 的日期部分
    const date = baseDate.split('T')[0] // 取日期部分
    return `${date} ${task.send_time}:00`
  } else if (task.id) {
    // update 任务，从 existing_task 中查找对应的时间
    const existingTask = existingTasks.find((t) => t.id === task.id)
    if (existingTask) {
      return existingTask.time
    }
  }

  // 默认返回基础时间
  return baseDate
}

/**
 * 获取真实聊天记录
 */
export async function getRealChatHistory(chatId: string, executionTime: string): Promise<string> {
  try {
    // 根据执行时间往前获取聊天记录
    const endTime = new Date(executionTime)
    const startTime = new Date(endTime.getTime() - 24 * 60 * 60 * 1000) // 往前24小时

    // 调用真实的聊天记录服务，获取最多6条记录，包含2次客户发言
    const history = await ChatHistoryService.getChatHistoryByTimeRange(chatId, startTime, endTime, 6, 2)

    // 格式化为字符串
    if (history && history.length > 0) {
      const formattedHistory = history.map((message) => {
        const role = message.role === 'user' ? '客户' : 'AI助手'
        const time = new Date(message.created_at).toLocaleString('zh-CN')
        return `[${time}] ${role}: ${message.content}`
      }).join('\n')

      return formattedHistory
    }

    return ''
  } catch (error) {
    console.error('获取聊天记录失败:', error)
    return ''
  }
}

/**
 * 获取真实客户画像
 */
export async function getRealCustomerPortrait(chatId: string, executionTime: string): Promise<string> {
  try {
    // 根据时间往前取一条客户消息，从中获取 chat_state 里的客户画像
    const endTime = new Date(executionTime)
    const startTime = new Date(endTime.getTime() - 24 * 60 * 60 * 1000) // 往前24小时

    // 获取最近的一条客户消息
    const recentMessages = await ChatHistoryService.getChatHistoryByTimeRange(chatId, startTime, endTime, 1, 1)

    if (recentMessages && recentMessages.length > 0) {
      const recentMessage = recentMessages[0]

      // 从消息中获取 chat_state
      if (recentMessage.chat_state && (recentMessage.chat_state as any).moreUserSlots) {
        return JSON.stringify((recentMessage.chat_state as any).moreUserSlots)
      }
    }

    await ChatStatStoreManager.initState(chatId)

    // 如果没有找到，尝试直接从 ChatStateStore 获取
    const chatState = ChatStateStore.get(chatId)
    if (chatState && chatState.moreUserSlots) {
      return JSON.stringify(chatState.moreUserSlots)
    }

    // 返回空对象的字符串作为fallback
    return '{}'
  } catch (error) {
    console.error('获取客户画像失败:', error)
    return '{}'
  }
}

/**
 * 生成时间信息字符串
 */
export function generateTemporalInformation(executionTime: string): string {
  const execTime = new Date(executionTime)
  const timeInfo = {
    currentTime: executionTime,
    dayOfWeek: execTime.getDay(),
    hour: execTime.getHours(),
    timeOfDay: execTime.getHours() < 12 ? 'morning' : execTime.getHours() < 18 ? 'afternoon' : 'evening',
    weekdayName: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][execTime.getDay()],
    formattedTime: execTime.toLocaleString('zh-CN')
  }

  return JSON.stringify(timeInfo)
}

/**
 * 将 Planner 输出的任务转换为 FreeKick 输入
 */
export async function convertPlannerTasksToFreeKickInputs(
  plannerOutput: PlanResponse,
  originalInput: any,
  baseDate: string,
  roundId: string
): Promise<Array<{ taskId: string; taskDescription: string; input: any; executionTime: string }>> {
  const tasks: Array<{ taskId: string; taskDescription: string; input: any; executionTime: string }> = []

  // 解析 existing_task
  let existingTasks: ExistingTask[] = []
  try {
    if (originalInput.existing_task) {
      existingTasks = JSON.parse(originalInput.existing_task)
    }
  } catch (error) {
    console.error('解析 existing_task 失败:', error)
  }


  // 处理新增任务
  if (plannerOutput.plans.toAdd) {
    for (let i = 0; i < plannerOutput.plans.toAdd.length; i++) {
      const task = plannerOutput.plans.toAdd[i]
      const executionTime = parseTaskExecutionTime(task, existingTasks, baseDate)

      tasks.push({
        taskId: `add_${i + 1}`,
        taskDescription: task.content,
        executionTime,
        input: {
          chatId: originalInput.chatId,
          mainTask: task.content,
          courseConfig: JSON.stringify(await ContextBuilder.courseConfigFreeKick(originalInput.chatId)),
          metaActions: '',
          availableMaterials: await PlannerMaterialRag.extractMaterial(originalInput.chatId, task.content, roundId),
          retrievedKnowledge: await KnowledgeRag.search(task.content, originalInput.chatId, roundId),
          customerBehavior: JSON.stringify(await ContextBuilder.getCustomerBehavior(originalInput.chatId)),
          customerPortrait: await getRealCustomerPortrait(originalInput.chatId, executionTime),
          dialogHistory: await getRealChatHistory(originalInput.chatId, executionTime),
          temporalInformation: generateTemporalInformation(executionTime)
        }
      })
    }
  }

  // 处理更新任务
  if (plannerOutput.plans.toUpdate) {
    for (const task of plannerOutput.plans.toUpdate) {
      const executionTime = parseTaskExecutionTime(task, existingTasks, baseDate)

      tasks.push({
        taskId: `update_${task.id}`,
        taskDescription: task.content,
        executionTime,
        input: {
          chatId: originalInput.chatId,
          mainTask: task.content,
          courseConfig: JSON.stringify(await ContextBuilder.courseConfigFreeKick(originalInput.chatId)),
          metaActions: '',
          availableMaterials: await PlannerMaterialRag.extractMaterial(originalInput.chatId, task.content, roundId),
          retrievedKnowledge: await KnowledgeRag.search(task.content, originalInput.chatId, roundId),
          customerBehavior: JSON.stringify(await ContextBuilder.getCustomerBehavior(originalInput.chatId)),
          customerPortrait: await getRealCustomerPortrait(originalInput.chatId, executionTime),
          dialogHistory: await getRealChatHistory(originalInput.chatId, executionTime),
          temporalInformation: generateTemporalInformation(executionTime)
        }
      })
    }
  }

  return tasks
}



/**
 * 批量执行 Planner + FreeKick 流程
 */
export async function runPlannerFreeKickBatch(params: PlannerFreeKickBatchParams): Promise<BatchResult[]> {
  const { chatId, selectedDays, mainTask } = params

  try {
    // 生成统一的 roundId 用于整个批量执行
    const batchRoundId = uuidv4()
    console.log(`开始批量执行，roundId: ${batchRoundId}`)

    // 1. 从 LangSmith 拉取数据
    const langsmithRuns = await fetchPlannerInputsFromLangSmith(chatId)

    if (langsmithRuns.length === 0) {
      throw new Error(`No Planner input data found for chat_id: ${chatId}`)
    }

    const results: BatchResult[] = []

    // 2. 为每个选中的天数执行流程
    for (let i = 0; i < selectedDays.length && i < langsmithRuns.length; i++) {
      const day = selectedDays[i]
      const langsmithRun = langsmithRuns[i]

      const result: BatchResult = {
        chatId,
        day,
        roundId: batchRoundId,
        plannerInput: langsmithRun.inputs,
        plannerOutput: null,
        freeKickResults: []
      }

      try {
        // 3. 真实调用 Planner
        console.log(`开始执行 ${day} 的 Planner...`)
        const plannerOutput = await callRealPlanner({
          userSlots: langsmithRun.inputs.user_slots,
          memory: langsmithRun.inputs.memory,
          userBehavior: langsmithRun.inputs.user_behavior,
          silentAnalyze: langsmithRun.inputs.silent_analyze,
          currentTime: langsmithRun.inputs.current_time,
          existingTask: langsmithRun.inputs.existing_task,
          chatId,
          roundId: batchRoundId,
          mainTask
        })

        result.plannerOutput = plannerOutput
        console.log(`${day} Planner 执行完成，生成 ${(plannerOutput.plans.toAdd?.length || 0) + (plannerOutput.plans.toUpdate?.length || 0)} 个任务`)

        // 4. 将 Planner 输出转换为 FreeKick 输入
        const freeKickInputs = await convertPlannerTasksToFreeKickInputs(
          plannerOutput,
          {
            chatId,
            userSlots: langsmithRun.inputs.user_slots,
            memory: langsmithRun.inputs.memory,
            existing_task: langsmithRun.inputs.existing_task
          },
          langsmithRun.start_time as string,
          batchRoundId
        )

        console.log(`${day} 准备执行 ${freeKickInputs.length} 个 FreeKick 任务`)

        // 5. 并行执行所有 FreeKick 任务
        const freeKickPromises = freeKickInputs.map(async (freeKickInput) => {
          try {
            console.log(`执行 FreeKick 任务: ${freeKickInput.taskId} - ${freeKickInput.taskDescription}`)
            const output = await callRealFreeKick({
              ...freeKickInput.input,
              roundId: batchRoundId
            })

            return {
              taskId: freeKickInput.taskId,
              taskDescription: freeKickInput.taskDescription,
              input: freeKickInput.input,
              output,
              executionTime: freeKickInput.executionTime
            }
          } catch (error) {
            console.error(`FreeKick 任务 ${freeKickInput.taskId} 执行失败:`, error)
            return {
              taskId: freeKickInput.taskId,
              taskDescription: freeKickInput.taskDescription,
              input: freeKickInput.input,
              output: null,
              executionTime: freeKickInput.executionTime,
              error: error instanceof Error ? error.message : String(error)
            }
          }
        })

        result.freeKickResults = await Promise.all(freeKickPromises)
        console.log(`${day} 所有 FreeKick 任务执行完成`)

      } catch (error) {
        console.error(`${day} 执行失败:`, error)
        result.error = error instanceof Error ? error.message : String(error)
      }

      results.push(result)
    }

    console.log(`批量执行完成，共处理 ${results.length} 天，总计 ${results.reduce((sum, r) => sum + r.freeKickResults.length, 0)} 个任务`)
    return results

  } catch (error) {
    console.error('批量执行失败:', error)
    throw new Error(`Batch execution failed: ${error instanceof Error ? error.message : String(error)}`)
  }
}
