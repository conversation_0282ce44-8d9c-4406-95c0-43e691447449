import React from 'react'

interface JsonRendererProps {
  data: any
  maxHeight?: string
}

export const JsonRenderer: React.FC<JsonRendererProps> = ({ data, maxHeight = '300px' }) => {
  const renderValue = (value: any, key?: string): React.ReactNode => {
    if (value === null) return <span className="text-gray-500">null</span>
    if (value === undefined) return <span className="text-gray-500">undefined</span>

    if (typeof value === 'string') {
      // 处理换行符和长文本
      if (value.includes('\n') || value.length > 100) {
        return (
          <div className="whitespace-pre-wrap break-words text-sm leading-relaxed">
            <span className="text-green-600">"{value}"</span>
          </div>
        )
      }
      return <span className="text-green-600">"{value}"</span>
    }

    if (typeof value === 'number') {
      return <span className="text-blue-600">{value}</span>
    }

    if (typeof value === 'boolean') {
      return <span className="text-purple-600">{value.toString()}</span>
    }

    if (Array.isArray(value)) {
      if (value.length === 0) return <span className="text-gray-500">[]</span>

      return (
        <div className="ml-4">
          <span className="text-gray-700">[</span>
          {value.map((item, index) => (
            <div key={index} className="ml-4">
              <span className="text-gray-500">{index}:</span> {renderValue(item)}
              {index < value.length - 1 && <span className="text-gray-700">,</span>}
            </div>
          ))}
          <span className="text-gray-700">]</span>
        </div>
      )
    }

    if (typeof value === 'object') {
      const entries = Object.entries(value)
      if (entries.length === 0) return <span className="text-gray-500">{'{}'}</span>

      return (
        <div className="ml-4">
          <span className="text-gray-700">{'{'}</span>
          {entries.map(([objKey, objValue], index) => (
            <div key={objKey} className="ml-4">
              <span className="text-blue-800 font-medium">"{objKey}"</span>
              <span className="text-gray-700">: </span>
              {renderValue(objValue, objKey)}
              {index < entries.length - 1 && <span className="text-gray-700">,</span>}
            </div>
          ))}
          <span className="text-gray-700">{'}'}</span>
        </div>
      )
    }

    return <span className="text-gray-500">{String(value)}</span>
  }

  return (
    <div
      className="bg-gray-50 border rounded-lg p-4 font-mono text-sm overflow-auto"
      style={{ maxHeight }}
    >
      {renderValue(data)}
    </div>
  )
}

interface JsonFieldProps {
  label: string
  data: any
  defaultExpanded?: boolean
}

export const JsonField: React.FC<JsonFieldProps> = ({
  label,
  data,
  defaultExpanded = false
}) => {
  const [isExpanded, setIsExpanded] = React.useState(defaultExpanded)

  const hasContent = data && (
    typeof data === 'object' ? Object.keys(data).length > 0 : true
  )

  if (!hasContent) {
    return (
      <div className="mb-4">
        <h4 className="font-medium text-gray-700 mb-2">{label}</h4>
        <div className="text-gray-500 text-sm">无数据</div>
      </div>
    )
  }

  return (
    <div className="mb-4">
      <div className="flex items-center gap-2 mb-2">
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="flex items-center gap-1 font-medium text-gray-700 hover:text-gray-900"
        >
          <span className={`transform transition-transform ${isExpanded ? 'rotate-90' : ''}`}>
            ▶
          </span>
          {label}
        </button>
      </div>

      {isExpanded && (
        <JsonRenderer data={data} maxHeight="400px" />
      )}
    </div>
  )
}
