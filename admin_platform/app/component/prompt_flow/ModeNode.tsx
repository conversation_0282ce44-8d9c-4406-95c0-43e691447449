'use client'

import { memo } from 'react'
import { Handle, Position, type NodeProps } from '@xyflow/react'
import type { FlowMode, ModeNodeData } from '@/types/flow'

interface ModeNodeRenderData extends ModeNodeData {
  onChange?: (patch: Partial<ModeNodeData>) => void
}

const MODE_LABEL: Record<FlowMode, string> = {
  customerPersona: '客户画像测试',
  plannerFreeKick: 'Planner + FreeKick 测试',
  memoryTest: '记忆测试',
}

const PlannerOptions = ['day1', 'day2', 'day3', 'day4', 'day5', 'day6']

function ModeNode({ data }: NodeProps<ModeNodeRenderData>) {
  const handleChatIdChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    const rawValue = event.target.value
    const chatIds = rawValue
      .split(/\r?\n/)
      .map((item) => item.trim())
      .filter((item) => item.length > 0)
    data.onChange?.({ chatId: rawValue, chatIds })
  }

  const handleDanmuToggle = (event: React.ChangeEvent<HTMLInputElement>) => {
    data.onChange?.({ isDanmu: event.target.checked })
  }

  const handleLoadExistingPortraitToggle = (event: React.ChangeEvent<HTMLInputElement>) => {
    data.onChange?.({ loadExistingPortrait: event.target.checked })
  }

  const handleCustomerMemoryChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    data.onChange?.({ customerMemory: event.target.value })
  }



  const handleModeSwitch = (event: React.ChangeEvent<HTMLSelectElement>) => {
    data.onChange?.({ mode: event.target.value as FlowMode })
  }

  const handleDateChange = (field: 'startDate' | 'endDate') =>
    (event: React.ChangeEvent<HTMLInputElement>) => {
      data.onChange?.({ timeRange: { [field]: event.target.value } as ModeNodeData['timeRange'] })
    }

  const handleTimePointChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    data.onChange?.({ timePoint: event.target.value })
  }

  return (
    <div className="min-w-[280px] rounded-md border border-slate-300 bg-white shadow-sm">
      <Handle type="source" position={Position.Right} />
      {/* 可拖拽的标题区域 */}
      <div className="drag-handle cursor-move rounded-t-md bg-slate-100 px-3 py-2 border-b border-slate-200">
        <h3 className="font-semibold text-slate-700">调试模式</h3>
      </div>
      {/* 内容区域 - 阻止拖拽 */}
      <div className="nodrag p-3">
        <label className="mb-3 flex flex-col gap-1 text-sm text-slate-600">
          <span>请选择调试场景</span>
          <select
            className="rounded border border-slate-300 px-2 py-1 text-sm"
            value={data.mode}
            onChange={handleModeSwitch}
          >
            {Object.entries(MODE_LABEL).map(([value, label]) => (
              <option key={value} value={value}>
                {label}
              </option>
            ))}
          </select>
        </label>

        <label className="mb-3 flex flex-col gap-1 text-sm text-slate-600">
          <span>客户 Chat ID（支持多行批量）</span>
          <textarea
            className="h-24 resize-none rounded border border-slate-300 p-2 text-sm font-mono"
            placeholder="每行一个 chatId"
            value={data.chatId ?? ''}
            onChange={handleChatIdChange}
          />
          <span className="text-xs text-slate-400">round_id 将在执行后自动生成并显示在结果中。</span>
        </label>

        {data.mode === 'customerPersona' ? (
          <div className="flex flex-col gap-2 text-sm text-slate-600">
            <label className="flex items-center gap-2 text-sm text-slate-600">
              <input
                type="checkbox"
                className="checkbox checkbox-sm"
                checked={Boolean(data.isDanmu)}
                onChange={handleDanmuToggle}
              />
              <span>弹幕模式</span>
            </label>
            <label className="flex items-center gap-2 text-sm text-slate-600">
              <input
                type="checkbox"
                className="checkbox checkbox-sm"
                checked={Boolean(data.loadExistingPortrait)}
                onChange={handleLoadExistingPortraitToggle}
              />
              <span>加载现有画像作为初始画像</span>
            </label>
            <span className="font-medium">时间范围</span>
            <label className="flex flex-col gap-1">
              <span>开始日期</span>
              <input
                type="date"
                className="rounded border border-slate-300 px-2 py-1 text-sm"
                value={data.timeRange?.startDate ?? ''}
                onChange={handleDateChange('startDate')}
              />
            </label>
            <label className="flex flex-col gap-1">
              <span>结束日期</span>
              <input
                type="date"
                className="rounded border border-slate-300 px-2 py-1 text-sm"
                value={data.timeRange?.endDate ?? ''}
                onChange={handleDateChange('endDate')}
              />
            </label>
          </div>
        ) : data.mode === 'memoryTest' ? (
          <div className="flex flex-col gap-2 text-sm text-slate-600">
            <label className="flex flex-col gap-1">
              <span>客户记忆（可选）</span>
              <textarea
                className="h-20 resize-none rounded border border-slate-300 p-2 text-sm"
                placeholder="输入客户记忆信息，留空则默认为空"
                value={data.customerMemory ?? ''}
                onChange={handleCustomerMemoryChange}
              />
            </label>
            <span className="font-medium">时间范围</span>
            <label className="flex flex-col gap-1">
              <span>开始日期</span>
              <input
                type="date"
                className="rounded border border-slate-300 px-2 py-1 text-sm"
                value={data.timeRange?.startDate ?? ''}
                onChange={handleDateChange('startDate')}
              />
            </label>
            <label className="flex flex-col gap-1">
              <span>结束日期</span>
              <input
                type="date"
                className="rounded border border-slate-300 px-2 py-1 text-sm"
                value={data.timeRange?.endDate ?? ''}
                onChange={handleDateChange('endDate')}
              />
            </label>
          </div>
        ) : (
          <div className="flex flex-col gap-3 text-sm text-slate-600">
            <label className="flex flex-col gap-1">
              <span className="font-medium">选择测试天数</span>
              <div className="grid grid-cols-3 gap-2">
                {PlannerOptions.map((option) => (
                  <label key={option} className="flex items-center gap-1 text-xs">
                    <input
                      type="checkbox"
                      className="checkbox checkbox-xs"
                      checked={data.selectedDays?.includes(option) ?? false}
                      onChange={(e) => {
                        const currentDays = data.selectedDays ?? []
                        const newDays = e.target.checked
                          ? [...currentDays, option]
                          : currentDays.filter((day) => day !== option)
                        data.onChange?.({ selectedDays: newDays })
                      }}
                    />
                    <span>{option}</span>
                  </label>
                ))}
              </div>
            </label>
          </div>
        )}
      </div>
    </div>
  )
}

export default memo(ModeNode)
