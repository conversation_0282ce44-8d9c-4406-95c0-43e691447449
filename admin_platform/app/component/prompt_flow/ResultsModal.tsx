'use client'

import { memo, useState, useEffect } from 'react'
import { createPortal } from 'react-dom'
import { X, ExternalLink, Copy, Check, Calendar, ChevronUp, ChevronDown, Clock } from 'lucide-react'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from './JsonRenderer'

interface PersonaRun {
  chatId: string
  portrait: Record<string, unknown>
  rawOutput: string
  chatHistory: unknown
  roundId: string
}

interface PlannerRun {
  chatId: string
  context: Record<string, unknown>
  mainTask: string
  plannerOutput: unknown
  freeKickOutput: unknown
  roundId: string
}

interface ExecutionStep {
  nodeId: string
  nodeType: string
  inputData: unknown
  outputData: unknown
  error?: string
}

interface BatchResult {
  chatId: string
  day: string
  roundId: string
  plannerInput: any
  plannerOutput: any
  freeKickResults: Array<{
    taskId: string
    taskDescription: string
    executionTime: string
    input: any
    output: any
    error?: string
  }>
  error?: string
}

interface ResultsModalProps {
  isOpen: boolean
  onClose: () => void
  personaRuns?: PersonaRun[]
  plannerRuns?: PlannerRun[]
  batchResults?: BatchResult[]
  trace?: ExecutionStep[]
}

function buildLangsmithLink(roundId?: string, template?: string) {
  if (!roundId) return null

  // 构建实际的 LangSmith 链接，基于提供的格式
  const baseUrl = 'https://smith.langchain.com/o/4a322f6a-86c2-4f11-b84f-50af060d25ec/projects/p/71c95623-65fd-41ff-af9c-6fee3e08e8ca'
  const columnVisibilityModel = encodeURIComponent('{"feedback_stats":false,"reference_example":false,"name":false,"in_dataset":false,"last_queued_at":false}')
  const timeModel = encodeURIComponent('{"duration":"14d"}')
  const searchFilter = `and(eq(is_root, true), eq(metadata_value, \\"${roundId}\\"))`
  const searchModel = encodeURIComponent(`{"filter":"${searchFilter}","searchFilter":"eq(is_root, true)"}`)

  return `${baseUrl}?columnVisibilityModel=${columnVisibilityModel}&timeModel=${timeModel}&runtab=2&searchModel=${searchModel}&tab=0`
}

function CopyButton({ text }: { text: string }) {
  const [copied, setCopied] = useState(false)

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(text)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy text:', err)
    }
  }

  return (
    <button
      onClick={handleCopy}
      className="inline-flex items-center gap-1 rounded px-2 py-1 text-xs text-slate-600 hover:bg-slate-100"
      title="复制内容"
    >
      {copied ? <Check className="h-3 w-3" /> : <Copy className="h-3 w-3" />}
      {copied ? '已复制' : '复制'}
    </button>
  )
}

function JsonViewer({ data, title }: { data: unknown; title: string }) {
  const jsonString = JSON.stringify(data, null, 2)

  return (
    <div className="space-y-2 min-w-0">
      <div className="flex items-center justify-between">
        <span className="font-medium text-slate-700">{title}</span>
        <CopyButton text={jsonString} />
      </div>
      <pre className="h-80 overflow-auto rounded-md bg-slate-900 p-4 text-sm text-white font-mono break-all">
        {jsonString}
      </pre>
    </div>
  )
}

function PersonaRunCard({ run }: { run: PersonaRun }) {
  const link = buildLangsmithLink(run.roundId)

  return (
    <div className="rounded-lg border border-emerald-200 bg-white p-6 shadow-sm hover:shadow-md transition-shadow">
      <div className="mb-6 flex items-center justify-between">
        <h4 className="text-lg font-semibold text-emerald-700">{run.chatId}</h4>
        <div className="flex items-center gap-3">
          {run.roundId && (
            <span className="text-sm text-slate-500">round_id: {run.roundId}</span>
          )}
          {link && (
            <a
              href={link}
              target="_blank"
              rel="noreferrer"
              className="inline-flex items-center gap-1 rounded border border-emerald-200 px-3 py-2 text-sm text-emerald-700 hover:bg-emerald-50"
            >
              <ExternalLink className="h-4 w-4" />
              LangSmith
            </a>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 2xl:grid-cols-2 gap-6">
        <JsonViewer data={run.portrait} title="画像解析" />

        <div className="space-y-2 min-w-0">
          <div className="flex items-center justify-between">
            <span className="font-medium text-slate-700">原始输出</span>
            <CopyButton text={run.rawOutput} />
          </div>
          <pre className="h-80 overflow-auto rounded-md bg-slate-900 p-4 text-sm text-white whitespace-pre-wrap font-mono break-all">
            {run.rawOutput}
          </pre>
        </div>
      </div>
    </div>
  )
}

function BatchResultCard({ result }: { result: BatchResult }) {
  const [isExpanded, setIsExpanded] = useState(false)
  const langsmithLink = result.roundId ? buildLangsmithLink(result.roundId) : null

  return (
    <div className="rounded-lg border border-slate-200 bg-white p-6 shadow-sm hover:shadow-md transition-shadow">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="rounded-full bg-purple-100 p-2">
            <Calendar className="h-4 w-4 text-purple-600" />
          </div>
          <div>
            <h4 className="font-medium text-slate-900">
              {result.chatId} - {result.day}
            </h4>
            <p className="text-sm text-slate-600">
              {result.freeKickResults.length} 个任务
            </p>
            {result.roundId && (
              <p className="text-xs text-slate-500">
                roundId: {result.roundId}
              </p>
            )}
          </div>
        </div>
        <div className="flex items-center gap-2">
          {langsmithLink && (
            <a
              href={langsmithLink}
              target="_blank"
              rel="noreferrer"
              className="inline-flex items-center gap-1 rounded border border-purple-200 px-2 py-1 text-xs text-purple-700 hover:bg-purple-50"
            >
              <ExternalLink className="h-3 w-3" />
              LangSmith
            </a>
          )}
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="rounded-md p-2 text-slate-400 hover:bg-slate-100 hover:text-slate-600"
          >
            {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
          </button>
        </div>
      </div>

      {result.error && (
        <div className="mb-4 rounded-md bg-red-50 p-3">
          <p className="text-sm text-red-700">错误: {result.error}</p>
        </div>
      )}

      {isExpanded && (
        <div className="space-y-6">
          {/* Planner 输入 */}
          <JsonField
            label="Planner 输入"
            data={result.plannerInput}
            defaultExpanded={false}
          />

          {/* Planner 输出 */}
          {result.plannerOutput && (
            <JsonField
              label="Planner 输出"
              data={result.plannerOutput}
              defaultExpanded={true}
            />
          )}

          {/* FreeKick 结果 */}
          <div>
            <h5 className="font-medium text-slate-900 mb-4">FreeKick 任务结果</h5>
            <div className="space-y-4">
              {result.freeKickResults.map((task, index) => (
                <div key={index} className="rounded-md border border-slate-200 p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <span className="text-sm font-medium text-slate-900">
                        {task.taskId}: {task.taskDescription}
                      </span>
                      {task.executionTime && (
                        <div className="flex items-center gap-1 mt-1">
                          <Clock className="h-3 w-3 text-slate-400" />
                          <span className="text-xs text-slate-500">
                            执行时间: {new Date(task.executionTime).toLocaleString('zh-CN')}
                          </span>
                        </div>
                      )}
                    </div>
                    {task.error && (
                      <span className="text-xs text-red-600 bg-red-50 px-2 py-1 rounded">
                        错误
                      </span>
                    )}
                  </div>

                  {task.error ? (
                    <p className="text-sm text-red-700">{task.error}</p>
                  ) : (
                    <div className="space-y-3">
                      <JsonField
                        label="FreeKick 输入"
                        data={task.input}
                        defaultExpanded={false}
                      />
                      <JsonField
                        label="FreeKick 输出"
                        data={task.output}
                        defaultExpanded={true}
                      />
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

function PlannerRunCard({ run }: { run: PlannerRun }) {
  const link = buildLangsmithLink(run.roundId)

  return (
    <div className="rounded-lg border border-blue-200 bg-white p-6 shadow-sm hover:shadow-md transition-shadow">
      <div className="mb-6 flex items-center justify-between">
        <h4 className="text-lg font-semibold text-blue-700">{run.chatId}</h4>
        <div className="flex items-center gap-3">
          {run.roundId && (
            <span className="text-sm text-slate-500">round_id: {run.roundId}</span>
          )}
          {link && (
            <a
              href={link}
              target="_blank"
              rel="noreferrer"
              className="inline-flex items-center gap-1 rounded border border-blue-200 px-3 py-2 text-sm text-blue-700 hover:bg-blue-50"
            >
              <ExternalLink className="h-4 w-4" />
              LangSmith
            </a>
          )}
        </div>
      </div>

      <div className="space-y-6">
        {run.mainTask && (
          <div className="space-y-2">
            <span className="font-medium text-slate-700">主要任务</span>
            <div className="rounded-md bg-slate-50 p-4 text-sm text-slate-700">
              {run.mainTask}
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 xl:grid-cols-2 2xl:grid-cols-3 gap-6">
          <JsonViewer data={run.context} title="上下文" />
          <JsonViewer data={run.plannerOutput} title="Planner 输出" />
          <JsonViewer data={run.freeKickOutput} title="FreeKick 输出" />
        </div>
      </div>
    </div>
  )
}

function TraceCard({ step }: { step: ExecutionStep }) {
  return (
    <div className="rounded-lg border border-slate-200 bg-white p-6 shadow-sm hover:shadow-md transition-shadow">
      <div className="mb-6">
        <h4 className="text-lg font-semibold text-slate-700">
          {step.nodeId} ({step.nodeType})
        </h4>
      </div>

      <div className="space-y-6">
        {step.error && (
          <div className="rounded-md border border-red-300 bg-red-50 p-4 text-sm text-red-700">
            <span className="font-medium">错误：</span>
            {step.error}
          </div>
        )}

        <div className="grid grid-cols-1 2xl:grid-cols-2 gap-6">
          <JsonViewer data={step.inputData} title="输入" />
          <JsonViewer data={step.outputData} title="输出" />
        </div>
      </div>
    </div>
  )
}

function ResultsModal({
  isOpen,
  onClose,
  personaRuns,
  plannerRuns,
  batchResults,
  trace
}: ResultsModalProps) {
  // 确保组件在客户端挂载后才创建 Portal (SSR 安全)
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)
  }, [])

  // ESC 键关闭弹窗
  useEffect(() => {
    if (!isOpen) return

    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose()
      }
    }

    document.addEventListener('keydown', handleEscape)
    return () => document.removeEventListener('keydown', handleEscape)
  }, [isOpen, onClose])

  if (!isOpen) return null

  const hasPersonaRuns = personaRuns && personaRuns.length > 0
  const hasPlannerRuns = plannerRuns && plannerRuns.length > 0
  const hasBatchResults = batchResults && batchResults.length > 0
  const hasTrace = trace && trace.length > 0

  const modalContent = (
    <div className="fixed inset-0 z-50">
      {/* 背景遮罩 */}
      <div
        className="absolute inset-0 bg-black bg-opacity-50"
        onClick={onClose}
      />

      {/* 弹窗内容 - 全屏 */}
      <div className="relative z-10 h-full w-full bg-white">
        {/* 头部 */}
        <div className="flex items-center justify-between border-b border-slate-200 px-6 py-4 bg-white sticky top-0 z-10">
          <h2 className="text-xl font-semibold text-slate-800">执行结果详情</h2>
          <button
            onClick={onClose}
            className="rounded-md p-2 text-slate-400 hover:bg-slate-100 hover:text-slate-600"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* 内容区域 */}
        <div className="h-[calc(100vh-80px)] overflow-y-auto p-6 bg-slate-50">
          <div className="mx-auto max-w-7xl space-y-8">
            {/* 客户画像结果 */}
            {hasPersonaRuns && (
              <section>
                <h3 className="mb-4 text-lg font-semibold text-emerald-700">客户画像结果</h3>
                <div className="space-y-4">
                  {personaRuns.map((run) => (
                    <PersonaRunCard
                      key={run.chatId}
                      run={run}
                    />
                  ))}
                </div>
              </section>
            )}

            {/* Planner + FreeKick 结果 */}
            {hasPlannerRuns && (
              <section>
                <h3 className="mb-4 text-lg font-semibold text-blue-700">Planner + FreeKick 结果</h3>
                <div className="space-y-4">
                  {plannerRuns.map((run) => (
                    <PlannerRunCard
                      key={run.chatId}
                      run={run}
                    />
                  ))}
                </div>
              </section>
            )}

            {/* 批量执行结果 */}
            {hasBatchResults && (
              <section>
                <h3 className="mb-4 text-lg font-semibold text-purple-700">批量执行结果</h3>
                <div className="space-y-4">
                  {batchResults.map((result, index) => (
                    <BatchResultCard
                      key={`${result.chatId}_${result.day}_${index}`}
                      result={result}
                    />
                  ))}
                </div>
              </section>
            )}

            {/* 执行链路 */}
            {hasTrace && (
              <section>
                <h3 className="mb-4 text-lg font-semibold text-slate-700">执行链路</h3>
                <div className="space-y-4">
                  {trace.map((step, index) => (
                    <TraceCard key={`${step.nodeId}-${index}`} step={step} />
                  ))}
                </div>
              </section>
            )}

            {/* 无结果提示 */}
            {!hasPersonaRuns && !hasPlannerRuns && !hasTrace && (
              <div className="py-12 text-center text-slate-500">
                <p>暂无执行结果</p>
                <p className="text-sm">执行流程后可在此查看详细结果</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )

  // 只有在客户端挂载后才创建 Portal
  if (isMounted) {
    return createPortal(modalContent, document.body)
  }

  return null
}

export default memo(ResultsModal)
