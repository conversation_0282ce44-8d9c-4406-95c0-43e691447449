'use client'

import { memo, useState } from 'react'
import { <PERSON><PERSON>, Position, type NodeProps } from '@xyflow/react'
import { Eye } from 'lucide-react'
import type { ExecutionTrace, OutputNodeData } from '@/types/flow'
import ResultsModal from './ResultsModal'

interface OutputNodeRenderData extends OutputNodeData {
  onClear?: () => void
}

function buildLangsmithLink(roundId?: string) {
  if (!roundId) return null

  // 构建实际的 LangSmith 链接，基于你提供的格式
  const baseUrl = 'https://smith.langchain.com/o/4a322f6a-86c2-4f11-b84f-50af060d25ec/projects/p/71c95623-65fd-41ff-af9c-6fee3e08e8ca'
  const columnVisibilityModel = encodeURIComponent('{"feedback_stats":false,"reference_example":false,"name":false,"in_dataset":false,"last_queued_at":false}')
  const timeModel = encodeURIComponent('{"duration":"14d"}')
  const searchFilter = `and(eq(is_root, true), eq(metadata_value, \\"${roundId}\\"))`
  const searchModel = encodeURIComponent(`{"filter":"${searchFilter}","searchFilter":"eq(is_root, true)"}`)

  return `${baseUrl}?columnVisibilityModel=${columnVisibilityModel}&timeModel=${timeModel}&runtab=2&searchModel=${searchModel}&tab=0`
}

function OutputNode({ data }: NodeProps<OutputNodeRenderData>) {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const trace = data.trace ?? []
  const meta = data.meta
  const personaRuns = Array.isArray(meta?.personaRuns) ? (meta.personaRuns as Array<any>) : null
  const plannerRuns = Array.isArray(meta?.plannerRuns) ? (meta.plannerRuns as Array<any>) : null
  const batchResults = Array.isArray(meta?.batchResults) ? (meta.batchResults as Array<any>) : null

  const hasResults = (personaRuns && personaRuns.length > 0) ||
                    (plannerRuns && plannerRuns.length > 0) ||
                    (batchResults && batchResults.length > 0) ||
                    trace.length > 0

  return (
    <>
      <div className="min-w-[240px] rounded-md border border-emerald-200 bg-emerald-50 shadow-sm">
        <Handle type="target" position={Position.Left} />
        {/* 可拖拽的标题区域 */}
        <div className="drag-handle cursor-move rounded-t-md bg-emerald-100 px-3 py-2 border-b border-emerald-200">
          <h3 className="font-semibold text-emerald-700">执行结果</h3>
        </div>
        {/* 内容区域 - 阻止拖拽 */}
        <div className="nodrag p-3">
          {hasResults ? (
            <div className="space-y-3">
              {/* 结果概览 */}
              <div className="text-sm text-emerald-700">
                {personaRuns && personaRuns.length > 0 && (
                  <div className="mb-2">
                    ✓ 客户画像结果：{personaRuns.length} 个
                  </div>
                )}
                {plannerRuns && plannerRuns.length > 0 && (
                  <div className="mb-2">
                    ✓ Planner + FreeKick 结果：{plannerRuns.length} 个
                  </div>
                )}
                {trace.length > 0 && (
                  <div className="mb-2">
                    ✓ 执行链路：{trace.length} 个步骤
                  </div>
                )}
              </div>

              {/* 查看详情按钮 */}
              <button
                onClick={() => setIsModalOpen(true)}
                className="flex w-full items-center justify-center gap-2 rounded-md bg-emerald-600 px-4 py-2 text-sm font-medium text-white hover:bg-emerald-700 transition-colors"
              >
                <Eye className="h-4 w-4" />
                查看详细结果
              </button>
            </div>
          ) : (
            <p className="text-sm text-emerald-700">执行后可在此查看完整结果。</p>
          )}
        </div>
      </div>

      {/* 结果弹窗 */}
      <ResultsModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        personaRuns={personaRuns || undefined}
        plannerRuns={plannerRuns || undefined}
        batchResults={meta?.batchResults || undefined}
        trace={trace}
      />
    </>
  )
}

export default memo(OutputNode)
