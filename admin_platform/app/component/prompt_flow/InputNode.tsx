'use client'

import { memo } from 'react'
import { Handle, Position, type NodeProps } from '@xyflow/react'
import type { InputNodeData } from '@/types/flow'

interface InputNodeRenderData extends InputNodeData {
  title?: string
  description?: string
  details?: unknown
}

function InputNode({ data }: NodeProps<InputNodeRenderData>) {
  const entries = Object.entries(data.params ?? {})

  return (
    <div className="min-w-[260px] rounded-md border border-slate-300 bg-white shadow-sm">
      <Handle type="target" position={Position.Left} />
      <Handle type="source" position={Position.Right} />
      {/* 可拖拽的标题区域 */}
      <div className="drag-handle cursor-move rounded-t-md bg-slate-100 px-3 py-2 border-b border-slate-200">
        <h3 className="font-semibold text-slate-700">{data.title ?? '输入概况'}</h3>
      </div>
      {/* 内容区域 - 阻止拖拽 */}
      <div className="nodrag p-3">
        {data.description && <p className="mb-3 text-sm text-slate-600">{data.description}</p>}
        {entries.length > 0 && (
          <div className="space-y-1 text-xs text-slate-500">
            {entries.map(([key, value]) => (
              <div key={key} className="flex justify-between gap-2">
                <span className="font-medium">{key}</span>
                <span className="truncate">{typeof value === 'string' ? value : JSON.stringify(value)}</span>
              </div>
            ))}
          </div>
        )}
        {typeof data.details !== 'undefined' && (
          <pre className="mt-3 max-h-48 overflow-auto whitespace-pre-wrap break-words rounded border border-slate-200 bg-slate-50 p-2 text-[11px] text-slate-600">
            {JSON.stringify(data.details, null, 2)}
          </pre>
        )}
      </div>
    </div>
  )
}

export default memo(InputNode)
