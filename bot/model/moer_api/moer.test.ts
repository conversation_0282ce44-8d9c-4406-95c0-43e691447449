import { <PERSON>r<PERSON><PERSON> } from './moer'
import { DataService } from '../../service/moer/getter/getData'

describe('Test', function () {
  beforeAll(() => {

  })
  it('system', async () => {
    console.log(await MoerAPI.getSystemCourseInfo())
  }, 60000)

  it('188course', async () => {
    console.log(await MoerAPI.get188CourseInfo())
  }, 60000)

  it('byId', async () => {
    console.log(JSON.stringify(await MoerAPI.getUserById('1123374'), null, 4))
  }, 60000)

  it('should pass', async () => {
    console.log(JSON.stringify(await MoerAPI.getUserByPhone('13225693198'), null, 4))
  }, 60000)

  it('course', async () => {
    // console.log(JSON.stringify(await <PERSON><PERSON><PERSON><PERSON>.getUserCourses('876611'), null, 4))
    console.log(await DataService.isPaidSystemCourse('7881299676145198_1688855025632783'))
  }, 60000)

  it('chapter info', async () => {
    console.log(JSON.stringify(await MoerAPI.getUserChapterStatus({
      'userId': '1113399',
      'liveId': '137',
      'sku': '20250523007940'
    }), null, 4))
  }, 60000)

  it('mark', async () => {
    console.log(JSON.stringify(await MoerAPI.getUserEnergyMark('781966'), null, 4))
  }, 60000)

  it('123', async () => {
    console.log(JSON.stringify(await MoerAPI.isOwnedCourse('781966', '20240604003478'), null, 4))
  }, 60000)

  it('course info', async () => {
    console.log(JSON.stringify(await MoerAPI.getCurrentCourseInfo(85), null, 4))
  }, 60000)

  it('isBindWechat', async () => {
    console.log(JSON.stringify(await MoerAPI.isBindWechat('781966'), null, 4))
  }, 60000)

  it('getCourseNoByMoreId', async () => {
    const userInfo = await MoerAPI.getUserById('1013674')
    const courseNo = userInfo.data.userGoodsSeries.find((item) => item.type === 1)?.stage
    console.log(courseNo)
  }, 60000)

  it('get course short link', async() => {
    console.log(await MoerAPI.get188CourseInfo())
  }, 60000)
})