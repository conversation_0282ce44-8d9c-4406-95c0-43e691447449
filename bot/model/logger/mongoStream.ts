// Create a custom writable stream for Feishu webhook
import stream from 'stream'
import { PrismaMongoClient } from '../mongodb/prisma'


export class MongoDBStream extends stream.Writable {
  async _write(chunk, encoding, callback) {
    const log = JSON.parse(chunk.toString())
    const levelLabels = {
      10: 'trace',
      20: 'debug',
      30: 'info',
      40: 'warn',
      50: 'error',
      60: 'fatal',
    }

    const level = levelLabels[log.level] || 'UNKNOWN'
    const timestamp = new Date(log.time)

    try {
      if (!log.chat_id) { // 有 chat_id 的信息再进行入库
        callback() // 在 Node.js 的 Stream 模型中，_write 方法有一个明确的约定：无论成功与否，每次调用 _write(chunk, encoding, callback) 都必须在函数体结束前调用 callback()，否则下一个 chunk 将不会再被写入，导致看起来 _write 只被调用一次。https://nodejs.cn/api/stream/writable_write_chunk_encoding_callback_1.html
        return
      }

      const plannerFlag = typeof log.planner === 'boolean'
        ? log.planner
        : (typeof log.planner === 'string' ? log.planner.toLowerCase() === 'true' : false)

      const roundId = typeof log.round_id === 'string' && log.round_id.length > 0
        ? log.round_id
        : undefined

      await PrismaMongoClient.getInstance().log_store.create({
        data: {
          level,
          timestamp,
          chat_id: log.chat_id,
          msg: log.msg,
          planner: plannerFlag ? true : undefined,
          round_id: roundId,
        }
      })

      callback()
    } catch (e) {
      console.error(e)
      callback()
    }
  }
}
