import { AzureChatOpenAI, ChatOpenAI } from '@langchain/openai'
import { Config } from '../../../config/config'
import { ChatAlibabaTongyi } from '@langchain/community/chat_models/alibaba_tongyi'

export const CLIENT_DEFAULT_TIMEOUT = 2 * 60 * 1000  // 2 minutes
export const OPENAI_MODELS = ['gpt-5', 'gpt-5-mini', 'gpt-5-nano', 'gpt-5-chat'] as const
export type OpenAIModelName = typeof OPENAI_MODELS[number]

interface IOpenAIInitParams {
  model?: string
  maxTokens?: number
  reasoningEffort?: string,
}

export class AzureOpenAIClient {
  public static getClient(params: IOpenAIInitParams = {}): AzureChatOpenAI {
    const fields: any = {
      model: params.model ?? 'gpt-5-mini',
      maxTokens: params.maxTokens ?? 1024,
      maxCompletionTokens: params.maxTokens ?? 1024,
      reasoning: { 'effort': params.reasoningEffort ?? 'minimal', 'summary': 'auto' },
      maxRetries: 2,
      useResponsesApi: true,
      timeout: CLIENT_DEFAULT_TIMEOUT,
      // configuration: { logLevel: 'debug' },  // 调试打开
      // Azure 认证参数
      azureOpenAIApiVersion: Config.setting.azureOpenAI.azureOpenAIApiVersion,
      azureOpenAIApiKey: Config.setting.azureOpenAI.azureOpenAIApiKey,
      azureOpenAIApiInstanceName: Config.setting.azureOpenAI.azureOpenAIApiInstanceName,
      azureOpenAIApiDeploymentName: params.model ?? 'gpt-5-mini',
      azureOpenAIBasePath: Config.setting.azureOpenAI.apiBaseUrl,
    }
    return new AzureChatOpenAI(fields)
  }
}

export class CheapOpenAI {
  public static getClient(params: IOpenAIInitParams = {}): ChatOpenAI {
    const fields: any = {
      model: params.model ?? 'gpt-5-mini',
      maxTokens: params.maxTokens ?? 1024,
      maxCompletionTokens: params.maxTokens ?? 1024,
      reasoning: { 'effort': params.reasoningEffort ?? 'minimal', 'summary': 'auto' },
      maxRetries: 2,
      useResponsesApi: true,
      timeout: CLIENT_DEFAULT_TIMEOUT,
      // OpenAI 认证参数
      apiKey: Config.setting.cheapOpenAI.apiKey,
      configuration: { baseURL: Config.setting.cheapOpenAI.apiBaseUrl },
    }
    return new ChatOpenAI(fields)
  }
}

export class OpenAIClient {
  public static getClient(params: IOpenAIInitParams = {}): ChatOpenAI {
    const fields: any = {
      model: params.model ?? 'gpt-5-mini',
      maxTokens: params.maxTokens ?? 1024,
      maxCompletionTokens: params.maxTokens ?? 1024,
      reasoning: { 'effort': params.reasoningEffort ?? 'minimal', 'summary': 'auto' },
      maxRetries: 2,
      useResponsesApi: true,
      timeout: CLIENT_DEFAULT_TIMEOUT,
      // OpenAI 认证参数
      apiKey: Config.setting.openai.apiKeys,
      configuration: { baseURL: Config.setting.openai.apiBaseUrl },
    }
    return new ChatOpenAI(fields)
  }
}

export class MiTaAI {
  public static getClient(model : 'concise' | 'detail' | 'research'): ChatOpenAI {
    return new ChatOpenAI({
      openAIApiKey: Config.setting.cheapOpenAI.apiKey,
      configuration: {
        baseURL: 'http://112.124.32.162:8000/v1',
        defaultHeaders: {
          Authorization: 'Bearer 61638845b28fa859c374a79f-0abc662597fb4d4ca498a786cbffb761'
        }
      },
      model: model,
      timeout: CLIENT_DEFAULT_TIMEOUT // 1.5 minutes
    })
  }
}

export class QwenMax {
  public static getClient() {
    return new ChatAlibabaTongyi({
      alibabaApiKey: Config.setting.qwen.apiKey,
      model: 'qwen-max',
    })
  }
}

export class PerplexityAI {
  public static getClient() {
    return new ChatOpenAI({
      openAIApiKey: '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',
      configuration: {
        baseURL: 'https://api.perplexity.ai'
      },
      model: 'llama-3-sonar-large-32k-online',
      timeout: CLIENT_DEFAULT_TIMEOUT // 1.5 minutes
    })
  }
}