import { MoerGeneralRAG } from '../../moer_general'
import ElasticSearchService from '../../../../../../model/elastic_search/elastic_search'
import { DataService } from '../../../../getter/getData'


export interface IKnowledgeRagTool {
    name: string
    description: string
    execute: (params: RagToolExecuteParams) => Promise<string>
}

export interface RagToolExecuteParams {
    chatId: string
    roundId: string
    strategy: string
    searchKey?: string
}

export class KnowledgeRagTool {

  public static GeneralSearch = 'rag搜索' //常规rag查询
  public static SearchCourseLink = '获取课程链接' //查询基础规则
  public static SearchKeyConcepts = '搜索课程关键概念'

  public static async getTools(): Promise<IKnowledgeRagTool[]> {
    return [
      await this.getGeneralSearchTool(),
      await this.getSearchCourseLinkTool(),
      await this.getSearchKeyConceptsTool(),
    ]
  }

  public static async getToolByKey(key: string): Promise<IKnowledgeRagTool | null> {
    const toolMap = {
      [KnowledgeRagTool.GeneralSearch]: await this.getGeneralSearchTool(),
      [KnowledgeRagTool.SearchCourseLink]: await this.getSearchCourseLinkTool(),
      [KnowledgeRagTool.SearchKeyConcepts]: await this.getSearchKeyConceptsTool(),
    }

    return Promise.resolve(toolMap[key])
  }


  private static async getGeneralSearchTool() {
    const name = KnowledgeRagTool.GeneralSearch
    const description = `用于查询知识、课程与概念性问题。
- 输入格式要求：
1.input: 必须是 单一、清晰的自然语言问题（疑问句），以“？”结尾。
2.只能包含单一概念/问题，问题应尽量准确简短。`

    const execute = async (params: RagToolExecuteParams) => {
      // 获取要查询的文档
      const queryDocs = await MoerGeneralRAG.getQueryDocs(params.chatId)

      // 构建查询 filter
      const filter = {
        bool:{
          must:[
            {
              terms: {
                'metadata.doc': queryDocs
              }
            }
          ]
        }
      }

      const res = await ElasticSearchService.embeddingSearch(
        MoerGeneralRAG.index,
        params.searchKey ?? '',
        2,
        0.74,
        filter
      )

      if (res.length === 0) {
        return `${name}没有找到相关结果`
      } else {
        return `${name}搜索结果：
${res.map((item) => {
    return `问题：${item.metadata.q}
答案：${item.metadata.a}`
  }).join('\n')}`
      }
    }


    return {
      name:name,
      description: description,
      execute: execute
    } as IKnowledgeRagTool
  }


  private static async getSearchCourseLinkTool() {
    const name = KnowledgeRagTool.SearchCourseLink
    const description = '获取课程直播/回放链接，当策略中需要提醒上课时，需要获取'

    const execute = async (params: RagToolExecuteParams) => {
      const currentTime = await DataService.getCurrentTime(params.chatId)

      if (currentTime.post_course_week) {
        return '课程已结束'
      }

      if (!currentTime.is_course_week) {
        return '课程未开始'
      }

      if (!currentTime.is_course_week && !currentTime.post_course_week) {
        const preCourseLink = await DataService.getCourseLink(0, params.chatId)
        return `小讲堂链接：${preCourseLink}`
      }

      const courseMap: Record<number, string> = {
        1: '情绪减压',
        2: '财富果园',
        3: '红靴子',
        4: '蓝鹰预演',
      }
      const results: string[] = []

      // 昨日课程回放链接
      const yesterdayDay = currentTime.day === 1 ? 7 : currentTime.day - 1
      if (yesterdayDay >= 1 && yesterdayDay <= 4) {
        const yesterdayLink = await DataService.getCourseLink(yesterdayDay, params.chatId, true)
        if (yesterdayLink) {
          results.push(`昨日课程（${courseMap[yesterdayDay]}）回放链接：${yesterdayLink}`)
        }
      }

      // 今日课程链接
      if (currentTime.day >= 1 && currentTime.day <= 4) {
        const todayLink = await DataService.getCourseLink(currentTime.day, params.chatId, false)
        const todayRecord = await DataService.getCourseLink(currentTime.day, params.chatId, true)
        if (todayLink) {
          results.push(`今日课程（${courseMap[currentTime.day]}）直播链接：${todayLink}`)
        }
        if (todayRecord) {
          results.push(`今日课程（${courseMap[currentTime.day]}）回放链接：${todayRecord}`)
        }
      }

      // 明日课程链接
      const tomorrowDay = currentTime.day === 7 ? 1 : currentTime.day + 1
      if (tomorrowDay >= 1 && tomorrowDay <= 4) {
        const tomorrowLink = await DataService.getCourseLink(tomorrowDay, params.chatId, false)
        if (tomorrowLink) {
          results.push(`明日课程（${courseMap[tomorrowDay]}）直播链接：${tomorrowLink}`)
        }
      }

      return results.join('\n')
    }

    return {
      name: name,
      description: description,
      execute: execute
    } as IKnowledgeRagTool
  }


  private static async getSearchKeyConceptsTool() {
    const name = KnowledgeRagTool.SearchKeyConcepts
    const description = '用于搜索课程关键概念（关键概念有：冥想入门营，冥想系统班），入参：需要搜索的课程概念(只能搜索已列举的概念)'

    const execute = async (params: RagToolExecuteParams) => {

      const systemStartTime = await DataService.getSystemCourseStartTime(params.chatId)


      const conceptMap:Record<string, string> = {
        '冥想入门营': `5天入门营（当前课程）
- 小讲堂：介绍后续3天课程的主要内容，普及冥想的作用，带领大家体验一次放松身心的海浪减压冥想，完成后会给客户发送完课礼《冥想练习指南》
- 能量测评：通过大卫·霍金斯的能量测评表，帮助同学清晰了解自己的能量状态，为后续课程的针对性学习打下基础。这部分内容会在小讲堂中详细讲解
- 开营仪式：周日20:00在社群内进行图文介绍，包括本次课程的授课导师信息、课程内容与收获，上课地点，以及如何提升学习效果的小技巧
- 第一课：周一20:00直播，主题为情绪减压。解析情绪和睡眠问题的根本原因，并手把手教大家冥想的正确姿势。带练冥想【沉浸式秒睡】，对缓解情绪压力和改善睡眠质量效果显著
- 第二课：周二20:00直播，主题为财富唤醒。聚焦于【财富问题】，解析富足的本质和吸引力法则，帮助学员逐步找到迷茫、负债等问题的内在原因。通过【财富果园】冥想带练，扫清对物质与财富的心理障碍，培养轻松获取和管理财富的正向思维
- 第三课：周三20:00直播，主题为红靴子飞跃。这是课程中最核心的一课，专注提升专注力和内在能量。带练冥想【红靴子飞跃】，帮助学员稳定内心、激发潜能。老师强调，如果一生只练一个冥想，红靴子冥想是最佳选择`,
        '冥想系统班': `21天系统班（后续课程）
- 系统班是三周21天的沉浸式旅程 ，每周5天课程稳步深入，每节课60～90分钟左右。是老师入门的开山之作。唐宁老师亲自带练。老师的目标是这12套功法要练一辈子
- 对零基础小白超友好：老师最骄傲的入门体系，过往的课程的回看有效期是仅仅只有一年，本次直接赠送永久回放，一次投入，终身复习，性价比拉满
- 正式课学习内容包含传承了千年的12套功法，将教会大家冥想3要素：体式，呼吸，念头
  - 禅宗三法：坐禅、立禅、卧禅，开启觉知、清零负能，攻克体式，进入不了状态的卡点。启动觉知、清空负能，解决“姿势找不到感觉”的难题
  - 五大呼吸：地火水风空五大呼吸，感受呼吸带来的能量，正能滋养，攻克呼吸短，浅，憋的卡点
  - 四大觉禅：音、光、息、念的冥想，更加精进，高能增频
- 价格：原价2880元，直播期间优惠1000元，现价1880元，是第一次有这样的优惠价格
- 赠品：限量赠送价值399元的采用抗震防潮材质的唐宁老师同款坐垫，价值144元的墨尔App季卡（含一百多个冥想音频）和21天系统班总结笔记。是老师四十多种课程里性价比最高的课程
- 智能直播加永久回放：每周提供四节课程和一节直播答疑，确保灵活学习
  - 系统化练习：周末设定练习打卡日，每天40-60分钟，帮你把核心心法真正落地到生活
  - 1对1助教跟进：全程关注你的进度，随时个性化答疑与指导
  - 学习社群：伙伴互助，共同进阶，修行不再孤单
- 21天课程系统班的开课时间：${systemStartTime}`
      }

      if (params.searchKey && params.searchKey in conceptMap) {
        return conceptMap[params.searchKey]
      }

      return '无结果'
    }

    return {
      name: name,
      description: description,
      execute: execute
    } as IKnowledgeRagTool
  }





}