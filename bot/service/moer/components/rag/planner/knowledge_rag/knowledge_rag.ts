import { IKnowledgeRagTool, KnowledgeRagTool, RagToolExecuteParams } from './knowledge_rag_tool'
import { SystemMessagePromptTemplate } from '@langchain/core/prompts'
import { LLM } from '../../../../../../lib/ai/llm/LLM'
import { Config } from '../../../../../../config/config'
import logger from '../../../../../../model/logger/logger'


interface PlanStep {

    reasoning: string
    plan: string
    op: string
    input: RagToolExecuteParams
    fallbacks?: string[]
}


interface Plan {
    steps: PlanStep[]
}


export class KnowledgeRag {


  public static async search(strategy: string, chatId: string, roundId: string) {

    const plan = await KnowledgeRag.generatePlan(strategy, await KnowledgeRagTool.getTools(), chatId, roundId)
    const evidence = await KnowledgeRag.executePlan(plan)

    this.logMessage('Evidence:', evidence)

    return evidence
  }


  private static async generatePlan(strategy: string, tools: IKnowledgeRagTool[], chatId: string, roundId: string): Promise<Plan> {
    // Compose a human friendly list of tool names for the prompt
    const toolList = tools
      .map((t) => `* ${t.name}: ${t.description}`)
      .join('\n')
    const plannerPrompt = `# 角色
你是一个计划专家，需要根据回复任务与客户的聊天记录生成知识搜索的计划。

# 任务
1. 将复杂问题拆解为若干个步骤，每个步骤只调用一个工具。
2. 全流程最多安排4个步骤；
3. op 字段必须是可用工具名称。
4. input 字段是传给工具的字符串。

# 可用工具
{{toolList}}

# 回复任务
{{strategy}}

# 聊天记录
{{chatHistory}}

# 严格按 JSON 格式输出
{
    "steps": [
        {
            "reasoning": "<简短阐述为什么需要此步骤>",
            "plan": "<步骤描述>",
            "op": "<工具键名>",
            "input": ""
        }
    ]
}
`

    const promptTemplate = SystemMessagePromptTemplate.fromTemplate(plannerPrompt, { templateFormat:'mustache' })

    const response = await LLM.predict(promptTemplate, {
      meta: { round_id: roundId },
      responseJSON: true,
      reasoningEffort: 'low',
      maxTokens: 3000
    },
    { toolList, strategy })

    this.logMessage('Rag Planner response:', response)
    logger.log('Rag Planner response:', response)

    // The LLM returns a JSON string; parse it into the Plan type.  In
    // case of malformed output, we fall back to an empty plan to
    // prevent runtime exceptions.s
    try {
      return this.plannerResponseToPlan(response, strategy, chatId, roundId)
    } catch (err) {
      console.warn('Failed to parse planner response', err)
      return { steps: [] }
    }
  }

  private static async executePlan(plan: Plan) {
    let result = ''
    for (const step of plan.steps) {
      const tool = await KnowledgeRagTool.getToolByKey(step.op)
      if (!tool) {
        continue
      }
      const toolResult = await tool.execute(step.input)
      result += `
操作：${step.op}
结果：${toolResult}`
    }

    return result
  }


  private static async plannerResponseToPlan(response: string, strategy: string, chatId: string, roundId: string) {
    const jsonData = JSON.parse(response)
    return {
      steps: jsonData.steps.map((step: any) => {
        const reasoning = step.reasoning
        const op = step.op
        const plan = step.plan

        return {
          reasoning,
          plan,
          op,
          input: {
            chatId,
            roundId,
            strategy,
            searchKey:step.input
          },
        }
      })
    } as Plan
  }


  private static logMessage(key: string, data: any) {
    if (Config.setting.localTest) {
      console.log(key, data)
    }
  }



}