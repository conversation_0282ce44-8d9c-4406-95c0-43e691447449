import { PromptTemplate } from '@langchain/core/prompts'
import { DataService } from '../../../getter/getData'
import { isScheduleTimeBefore } from '../../schedule/creat_schedule_task'
import { LLM } from '../../../../../lib/ai/llm/LLM'
import logger from '../../../../../model/logger/logger'
import ElasticSearchService from '../../../../../model/elastic_search/elastic_search'
import { sendMaterial, sendMaterialById } from '../../visualized_sop/visualized_sop_processor'
import { getUserId } from '../../../../../config/chat_id'
import { sleep } from '../../../../../lib/schedule/schedule'
import { materialPromptOutput } from './test/planner.test'
import { ContextBuilder } from '../../agent/context'


export interface IMaterial {
  title: string
  description: string
  source_id: string
}


export class PlannerMaterialRag {

  public static materialIndex = 'moer_material'


  public static async extractMaterial(chatId: string, plan: string, roundId: string): Promise<string> {

    const timeStr = await ContextBuilder.temporalInformation(chatId)
    const prompt = await PlannerMaterialRag.extractMaterialFromPlanPrompt(plan, timeStr)

    const response = await LLM.predict(prompt, { responseJSON: true, model:'gpt-5-mini', reasoningEffort:'low', maxTokens:3000, meta:{ round_id:roundId } })

    try {
      const data = JSON.parse(response)

      let materialInfo = ''
      for (let i = 0; i < data.titles.length; i++) {
        const material = await PlannerMaterialRag.searchMaterialFromES(data.titles[i])

        if (!material) {
          continue
        }

        materialInfo += `- [${material.title}]：${material.description}\n`
      }

      return materialInfo
    } catch (e) {
      logger.error({ chat_id: chatId }, '解析素材JSON格式失败')
    }

    return ''
  }


  public static async sendMaterial(chatId: string, materialNames: string[]) {

    const materials: IMaterial[] = []

    for (let i = 0; i < materialNames.length; i++) {
      const material = await PlannerMaterialRag.searchMaterialFromES(materialNames[i])
      if (material) {
        materials.push(material)
      }
    }

    for (let i = 0; i < materials.length; i++) {
      await sleep(2000)
      await sendMaterialById(getUserId(chatId), chatId, materials[i].source_id)
    }

  }


  private static async searchMaterialFromES(query: string): Promise<IMaterial | null> {

    const searchRes =  await ElasticSearchService.embeddingSearch(
      PlannerMaterialRag.materialIndex,
      query,
      1,
      0.8,
    )

    if (searchRes.length > 0) {
      return {
        title: searchRes[0].metadata.title,
        description: searchRes[0].metadata.description,
        source_id: searchRes[0].metadata.source_id
      }
    }

    return null
  }




  private static async extractMaterialFromPlanPrompt(plan: string, timeStr: string) {

    const prompt = `# 角色
你是一个“素材调用助手”。  

# 任务
根据给定的 **目标描述**和当前时间，参考参考素材，遵守使用规则，联想出可能需要的素材，并返回对应素材库中素材的 title 列表，用于后续搜索和调用。

# 目标描述
{plan}

# 当前时间
{timeStr}

# 课程设置
- 除非客户明确请假需要看回放，否则优先引导客户直播到课
- 第一课（周一 20:00~21:45）情绪减压：解析情绪与睡眠问题根源，带练【沉浸式秒睡】完课会赠送《身心对照表》
- 第二课（周二 20:00~21:15）财富唤醒：解析财富心理障碍，带练【财富果园】课后可针对冥想过程中的果园画面（果实，果树，大门，呼吸顺畅，四季循环等）做一对一文字解读；完课会赠送《阿尔法音频》
- 第三课（周三 20:00~22:20）红鞋子飞跃：提升专注力与能量，带练【红鞋子飞跃】21点左右老师会开始介绍系统班；完课会赠送《红鞋子音频》。本节课核心亮点：提升专注力与能量是成功的基石
- 加播课（第四课）（周四 20:00，高阶加播课）蓝鹰预演：提高行动力，预演未来（周三22:20前不公开）。本节课核心亮点：成事的秘密。
- 系统班销售启动时间：周三21:00后允许；加播课（周四）及之后 → 系统班销售为主

# 参考素材（包括但不限于）
## 上课周前：
- 冥想科普相关资料:什么人适合练习冥想图、冥想为什么能解决财富问题、冥想是什么、冥想练习前的准备和正确姿势等
- 入门营课表（仅在第一课开课前可发送）
- 入门营课程安排（仅在第一课开课前可发送）
- 能量测评图解（仅在第一课开课前可发送）
- 海浪减压冥想音频
## 入门营开课期间：
- 第一节课《情绪减压》课前：第一课课程预告与介绍、第一课引导到课素材等
- 第一节课《情绪减压》课后：第一课思维导图、第一课沉浸式秒睡练习音频、第一课完课奖励等
- 第二节课《财富唤醒》课前：第二课课程预告与介绍、第二课引导到课素材等
- 第二节课《财富唤醒》课后：第二课思维导图、第二课财富果园练习音频、第二课完课奖励等
- 第三节课《效能提升》课前：第三课课程预告与介绍、第三课引导到课素材等
- 第三节课《效能提升》课后：第三课思维导图、第三课红靴子冥想练习音频等
- 加播课（第四课）《成事显化（蓝鹰预演）》课前：加播课（第四课）课程预告与介绍、加播课（第四课）引导到课素材等
- 加播课（第四课）《成事显化（蓝鹰预演）》课后：加播课（第四课）思维导图、蓝鹰预演冥想练习音频等
## 系统班相关素材（第三课结课后、第四课结课后、课程已结束时发送）：系统班课程表，系统班课程体系，系统班报名福利，系统班学员反馈等
## 解决学员个性化痛点类素材：睡眠问题、困境低谷、拖延、焦虑受害者思维、财富卡点、情感关系等
## 唐宁老师素材：唐宁老师介绍，唐宁老师与学员交流，唐宁人设塑造等

# 使用规则：
1. 目标输入可能来自「课程规划器」生成的 task
2. 你需要基于目标的语义，参考当前时间（注意区分课前和课后），匹配到最相关的素材标题，遵守下面规则：  
- **相似或重复的素材只保留一个**，确保输出列表中没有功能重叠的素材。  
- **每类需求只输出 1 个最能代表的素材**，保证覆盖目标需求即可。  
- 输出的素材标题必须是一句精简、完整的话，**不得包含括号说明**。
- 每节课课前不要给课后素材
- 当前时间为课程已结束时，只可给出系统班相关素材、解决学员个性化痛点类素材、唐宁老师素材
3. 输出时保持结构化：
   - reason（简要说明为什么选择这些素材，便于调试）
   - titles（一组 title，用于在素材库中搜索）

### 以JSON格式输出：
{{
    "reason": "简要逻辑"
    "titles": ["素材title1", "素材title2", "素材title3"],
}}
`
    const promptTemplate = PromptTemplate.fromTemplate(prompt)


    return await promptTemplate.format({
      plan, timeStr
    })
  }


  public static async getTimeInfo(chatId: string) {
    const currentTime = await DataService.getCurrentTime(chatId)

    if (!currentTime.is_course_week) {
      return '当前非上课周'
    }

    if (isScheduleTimeBefore(currentTime, { is_course_week: true, day: 1, time: '23:59:00' })) {
      return '当前第一节课，情绪减压'
    } else if (isScheduleTimeBefore(currentTime, { is_course_week: true, day: 2, time: '23:59:00' })) {
      return '当前第二节课，财富果园'
    } else if (isScheduleTimeBefore(currentTime, { is_course_week: true, day: 3, time: '23:59:00' })) {
      return '当前第三节课，红靴子'
    } else if (isScheduleTimeBefore(currentTime, { is_course_week: true, day: 4, time: '23:59:00' })) {
      return '当前第四节课，蓝鹰预演'
    } else {
      return '课程已结束'
    }

  }




}