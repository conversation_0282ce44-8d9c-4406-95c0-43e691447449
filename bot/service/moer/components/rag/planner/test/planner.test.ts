import { FileHelper } from '../../../../../../lib/file'
import { PlannerMaterialRag } from '../material_rag'
import { TaskWorker } from '../../../planner/task/task_worker'
import { KnowledgeRag } from '../knowledge_rag/knowledge_rag'
import { Config } from '../../../../../../config/config'
import { loadConfigByAccountName } from '../../../../../../../test/tools/load_config'
import { DataService } from '../../../../getter/getData'


export const materialPromptOutput: string[] = []

describe('ragPlannerTest', () => {


  it('batchMaterialTest', async () => {
    const data = await FileHelper.readFile('/Users/<USER>/Desktop/wechaty_bot/bot/service/moer/components/rag/planner/test/data/素材测试.json')

    const jsonData = JSON.parse(data)

    const resList: any[] = []

    for (const item of jsonData) {

      const res = {
        task: item.task,
        timeInfo: item.timeInfo,
        expected: item.expect,
      }

      PlannerMaterialRag.getTimeInfo = async (chatId: string) => {
        return item.timeInfo
      }

      KnowledgeRag.search = async (strategy: string, chatId: string, roundId: string) => {
        return ''
      }


      const sentMaterial: string[] = []
      PlannerMaterialRag.sendMaterial = async (chatId: string, materialNames: string[]) => {
        sentMaterial.push(...materialNames)
      }

      let extractedMaterial = ''
      const originalFunc = PlannerMaterialRag.extractMaterial
      PlannerMaterialRag.extractMaterial = async (chatId: string, plan: string, roundId: string) => {
        const res = await originalFunc(chatId, plan, roundId)
        extractedMaterial =  res
        return res
      }

      const outputContent = await TaskWorker.processTask('', item.task)


      res['extractedMaterial'] = extractedMaterial
      res['sentMaterial'] = JSON.stringify(sentMaterial)
      res['outputContent'] = outputContent
      res['materialOutput'] = materialPromptOutput[0]

      materialPromptOutput.length = 0

      resList.push(res)
    }
    await FileHelper.writeFile('/Users/<USER>/Desktop/wechaty_bot/bot/service/moer/components/rag/planner/test/data/素材测试结果.json', JSON.stringify(resList))




  }, 9e8)


  it('materialTest', async () => {

    Config.setting.localTest = false
    Config.setting.wechatConfig = await loadConfigByAccountName('Horus')

    DataService.getCurrentTime = async (chatId: string) => {
      return {
        is_course_week: true,
        day:4,
        time:'10:00:00'
      }
    }

    const task = '通知加播课并构建下一步阶梯：明确今晚加播课是体验营到系统班之间的‘行动力模块补齐’，要求客户在两条路径中做选择并回复：A 20:00到课；B 录播补课+次日指定复练（给出具体时间窗口与练习名称），承诺在次日上午反馈练习结果。'

    const outputContent = await TaskWorker.processTask('7881300516060552_1688858254705213', task)

    console.log(outputContent)

  }, 9e8)

  it('sendMaterial', async () => {

    Config.setting.wechatConfig = await loadConfigByAccountName('Horus')

    const title = ['痛点：睡眠']

    await PlannerMaterialRag.sendMaterial('7881300516060552_1688858254705213', title)

  }, 60000)

})