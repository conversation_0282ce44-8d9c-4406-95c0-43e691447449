import { ContextBuilder } from '../../../agent/context'
import { DataService } from '../../../../getter/getData'
import { ChatStatStoreManager } from '../../../../storage/chat_state_store'


describe('PromptTest', function () {
  const chat_id = '7881301655007754_1688857949631398'
  beforeAll(async () => {
    await ChatStatStoreManager.initState(chat_id)
  })
  it('1.TestGetCourseSummaryInfo', async () => {
    // const user_id = UUID.short()
    // const chat_id = getChatId(user_id)
    const chats = await DataService.getChatByWechatName('夏青 金牌喜嫁鲜花💐 鸿锦婚礼')
    if (!chats) {
      return
    }
    const chat =  chats[0] as any
    const chatId = chat._id
    await ChatStatStoreManager.initState(chatId)
    console.log(await ContextBuilder.getCustomerBehavior(chatId))
  }, 60000)


  it('测试获取上课信息的瓶颈在哪', async () => {
    console.log(await ContextBuilder.getCustomerBehavior('7881302298050442_1688858254705213'))
  }, 30000)

  it('get-user-slots', async () => {
    const chat_id = '7881302298050442_1688858254705213'
    // const user_id = UUID.short()
    // const chat_id = getChatId(user_id)
    await ChatStatStoreManager.initState(chat_id)
    console.log(ContextBuilder.getCustomerPortrait(chat_id))
  })

  it('2. 测试getRule函数', async () => {
    console.log(await ContextBuilder.getRules('test'))
  })


  it('3. 测试getStagePrompt', async () => {
    const stagePromptData = await ContextBuilder.getDefaultStagePromptData(chat_id)
    if (stagePromptData) {
      const stagePrompt = await ContextBuilder.getStagePrompt(chat_id, stagePromptData)
      console.log(stagePrompt)
    } else {
      console.log('获取阶段prompt失败')
    }
  })

  it('4. 测试getStagePrompt brief版本', async () => {
    const stagePromptData = ContextBuilder.getBriefStagePromptData()
    const stagePrompt = await ContextBuilder.getStagePrompt(chat_id, stagePromptData)
    console.log(stagePrompt)
  })

  it('5. 测试getCustomerSilentDuration', async () => {
    // 测试默认阈值（24小时）
    const silentDuration = await ContextBuilder.getCustomerSilentDuration(chat_id)
    console.log('客户沉默时长 (默认阈值):', silentDuration)

    // 测试自定义阈值（1小时）
    const silentDurationCustom = await ContextBuilder.getCustomerSilentDuration(chat_id, 1)
    console.log('客户沉默时长 (1小时阈值):', silentDurationCustom)

    // 测试很高的阈值（200天），应该显示未达到沉默阈值
    const silentDurationHigh = await ContextBuilder.getCustomerSilentDuration(chat_id, 200 * 24)
    console.log('客户沉默时长 (200天阈值):', silentDurationHigh)

    // 验证返回格式
    expect(silentDuration).toMatch(/^客户沉默时长：/)
    expect(silentDuration).toMatch(/(分钟|小时|天|无历史消息|获取失败|未达到沉默阈值)$/)

    expect(silentDurationCustom).toMatch(/^客户沉默时长：/)
    expect(silentDurationCustom).toMatch(/(分钟|小时|天|无历史消息|获取失败|未达到沉默阈值)$/)

    expect(silentDurationHigh).toMatch(/^客户沉默时长：/)
    expect(silentDurationHigh).toMatch(/(分钟|小时|天|无历史消息|获取失败|未达到沉默阈值)$/)
  })
})