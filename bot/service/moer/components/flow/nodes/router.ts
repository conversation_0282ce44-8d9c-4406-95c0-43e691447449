import { IWorkflowState } from '../flow'
import { LLM } from '../../../../../lib/ai/llm/LLM'
import { XMLHelper } from '../../../../../lib/xml/xml'
import { ChatStateStore } from '../../../storage/chat_state_store'
import { HumanTransfer, HumanTransferType } from '../../human_transfer/human_transfer'
import logger from '../../../../../model/logger/logger'
import { StringHelper } from '../../../../../lib/string'
import { MoerNode } from './type'
import { CheckPreCourseCompletionTask } from '../schedule/task/checkPreCourseCompletion'
import { DataService } from '../../../getter/getData'
import { HomeworkTemplate } from '../helper/homeworkTemplate'
import { ChatHistoryService } from '../../chat_history/chat_history'
import { MessageSender } from '../../message/message_send'
import { IWecomMsgType } from '../../../../../lib/juzi/type'
import { IScheduleTime, isScheduleTimeAfter, isScheduleTimeBefore } from '../../schedule/creat_schedule_task'
import { SystemMessagePromptTemplate } from '@langchain/core/prompts'
import { Config } from '../../../../../config/config'
import { sleep } from '../../../../../lib/schedule/schedule'
import { LLMXMLHelper } from '../helper/xmlHelper'
import { RegexHelper } from '../../../../../lib/regex/regex'
import { getPrompt } from '../../agent/prompt'
import { TaskManager } from '../../planner/task/task_manager'

export class Router {
  /**
   * 根据客户消息进行路由，特别注意这里的路由要写的 特定情况才能跳转，不能太通用，不然容易路由到错误的节点
   * 返回 End, 表示不执行任何节点逻辑
   * @param state
   */
  public static async route(state: IWorkflowState) {
    if (!state.userMessage) { return null }
    const currentTime = await DataService.getCurrentTime(state.chat_id)

    // 已付款，且没有处理完售后（发送地址的），路由回售后节点
    if (ChatStateStore.get(state.chat_id).state.is_complete_payment && !ChatStateStore.get(state.chat_id).state.is_complete_post_sale) {
      return MoerNode.PostSale
    }

    // 188 课程销售阶段
    // const isIn188CourseSalesDuration = await this.checkIsIn188CourseDuration(state.chat_id, currentTime)
    // if (isIn188CourseSalesDuration) {
    //   await HumanTransfer.transfer(state.chat_id, state.user_id, HumanTransferType.Course188Consulting)
    //   return MoerNode.DummyEnd
    // }

    // 检查手机号是否填入，没填入的话，转回到询问手机号节点
    const moerId = await DataService.getMoerIdByChatId(state.chat_id)
    if (!Config.isOYuanChannel() && !moerId && !ChatStateStore.getFlags(state.chat_id).is_bounding_phone_number && !Config.setting.localTest) return MoerNode.PhoneQuery

    // // 检查是否为回复问卷
    const isRepliedSurvey = await this.checkIsRepliedSurvey(state.chat_id, state.userMessage)
    if (isRepliedSurvey) {
      // 修改 userMessage
      state.userMessage = isRepliedSurvey

      return MoerNode.Dummy
    }

    // 检查作业模板
    const homeworkNode = await this.checkHomeworkTemplate(state.chat_id, state.userMessage)
    if (homeworkNode) return homeworkNode

    // 废话内容过滤
    const isChatter = await this.filterChatter(state.userMessage, state.chat_id)
    if (isChatter && isScheduleTimeBefore(currentTime, { is_course_week: true, day: 3, time: '21:00:00' })) return MoerNode.DummyEnd

    // 客户识别AI检查
    const robotNode = await this.checkRobotDetection(state.chat_id, state.round_id, state.user_id, state.userMessage)
    if (robotNode) return robotNode

    // 客户连续2次无法进入直播
    if (currentTime.is_course_week && currentTime.day <= 4 && currentTime.time >= '19:50:00' && currentTime.time <= '21:20:00') {
      const sendMiniProgramNode = await this.SendMiniProgramNode(state.chat_id, state.user_id, true)
      if (sendMiniProgramNode) return sendMiniProgramNode
    }

    // 客户主动说看完了，完成了，也进行小课堂完课礼发送
    const preCourseComplete = await this.checkCourseComplete(state.chat_id, state.user_id, state.userMessage, state.round_id)
    if (preCourseComplete) return preCourseComplete

    // 客户索要周卡
    const couponNode = await this.checkCouponCondition(state.chat_id, state.user_id, state.userMessage)
    if (couponNode) return couponNode

    // 上课期间检查客户是否说自己在课程中了
    await this.checkIsInCourse(state.chat_id, state.userMessage, state.round_id)

    // 分类路由
    return await this.routeByCategory(state.chat_id, state.user_id, state.userMessage, state.round_id)
  }

  // 检查作业模板
  private static async checkHomeworkTemplate(chatId: string, userMessage: string): Promise<MoerNode | null> {
    const homeworkTemplateType = await HomeworkTemplate.isHomeworkTemplate(chatId, userMessage)
    if (!homeworkTemplateType) {
      if (homeworkTemplateType === 'day1') {
        return MoerNode.CourseFeedBackDay1
      } else if (homeworkTemplateType === 'day2') {
        return MoerNode.WealthOrchardAnalyze
      }
    }
    return null
  }

  static async checkIsIn188CourseDuration(chatId:string, currentTime:IScheduleTime): Promise<boolean> {
    if (!currentTime.post_course_week || currentTime.day !== 2) {
      return false
    }

    const isAfterPostWeekOneTuesday17_30 = isScheduleTimeAfter(currentTime, {
      day: 2,
      time: '17:30:00',
      post_course_week: 1
    })

    const isBeforePostWeekOneTuesday23_59 = isScheduleTimeAfter({
      day: 2,
      time: '23:59:59',
      post_course_week: 1
    }, currentTime)

    const isPaid = await DataService.isPaidSystemCourse(chatId)
    return !isPaid && isAfterPostWeekOneTuesday17_30 && isBeforePostWeekOneTuesday23_59
  }

  // 废话内容过滤
  static async filterChatter(userMessage: string, chat_id: string): Promise<boolean> {
    const currentTasks = await TaskManager.getStringifyActiveTasks(chat_id)
    if (currentTasks) { // 有 Plan 不结束，继续执行 plan
      return false
    }

    const KEYWORDS = ['好的', 'OK', '收到', '谢谢', '感谢', '晚安', '谢谢老师', '嗯', '嗯嗯', '[OK]', '行', '嗯呢', '好', '👌', '[握手]', '好的，谢谢', '1', '🙏', '😊', '🌹', '❤️', '☕️', '[抱拳]']
    return KEYWORDS.some((keyword) => userMessage.toLowerCase() === keyword.toLowerCase())
  }

  // 客户识别AI检查
  public static async checkRobotDetection(chat_id: string, round_id: string, user_id: string, userMessage: string): Promise<MoerNode | null> {
    const robotDetectionNodeName = 'RobotDetected'
    const AI_KEYWORDS = ['人工', '机器人', '是真人', 'ai', '人机', '智能', '智慧体']
    const isRobotKeywordPresent = AI_KEYWORDS.some((keyword) => userMessage.toLowerCase().includes(keyword))

    if (isRobotKeywordPresent) {
      // LLM二次校验AI检查
      const chatHistory = await ChatHistoryService.getLLMChatHistory(chat_id, 2)
      const formattedChatHistory = ChatHistoryService.formatHistoryHelper(chatHistory)
      const robotDetectionPrompt = SystemMessagePromptTemplate.fromTemplate(`根据客户最近发言，判断其是否怀疑你是AI或者机器人，若怀疑则输出true，反之输出false
${formattedChatHistory}
先输出理由到 <reason></reason> 标签中，并将结果true或者false输出到 <answer></answer> 标签中

## 输出示例
<reason>客户说他之前做过AI，并没有怀疑你是AI</reason>
<answer>false</answer>

开始输出`, { templateFormat: 'mustache' })
      const res = await LLM.predict(robotDetectionPrompt, { meta: { promptName: 'robot_detection', chat_id: chat_id, round_id: round_id } }, {})
      const robotDetectionResult = XMLHelper.extractContent(res, 'answer')
      if (robotDetectionResult != 'true') {
        return null
      }

      ChatStateStore.increNodeCount(chat_id, robotDetectionNodeName)
      const robotDetectedNum = ChatStateStore.getNodeCount(chat_id, robotDetectionNodeName)

      if (robotDetectedNum >= 2) {
        try {
          await HumanTransfer.transfer(chat_id, user_id, HumanTransferType.RobotDetected, true, `客户说：${userMessage}`)
          ChatStateStore.update(chat_id, { nodeInvokeCount: { [robotDetectionNodeName]: 0 } })
          return null
        } catch (error) {
          logger.error('客户识别到AI，但转人工失败:', error)
        }
      } else if (robotDetectedNum === 1) {
        await HumanTransfer.transfer(chat_id, user_id, HumanTransferType.RobotDetected, 'onlyNotify', `客户说：${userMessage}`)
      }
    }
    return null
  }

  // 客户连续2次无法进入直播
  private static async SendMiniProgramNode(chat_id: string, user_id: string, live_check = false): Promise<MoerNode | null> {
    // 检查是否属于发送小程序的合理时间（上课周4的22:25之前）
    const currentTime = await DataService.getCurrentTime(chat_id)
    if ((currentTime.is_course_week && currentTime.day == 4 && currentTime.time >= '22:25:00') || (currentTime.is_course_week && currentTime.day > 4) || currentTime.post_course_week) {
      return null
    }
    const chatHistory = await ChatHistoryService.getLLMChatHistory(chat_id, 3)
    const formattedChatHistory = ChatHistoryService.formatHistoryHelper(chatHistory)
    let sendMiniProgram: string | null = 'true'
    if (live_check) {
      const res = await LLM.predict(`根据最近两轮麦子老师与客户的对话：
${formattedChatHistory}

判断在麦子老师邀请客户来听课时，客户是否连续2次由于技术问题无法进入直播间/无法正常上课，若是，则输出 true，反之，则输出 false

## 输出 true 的场景包含但不限于
- 客户直到聊天结束都无法进入直播间/看不到/听不了
- 客户发送图片且图片显示“购买课程后可观看”
- 客户表示要重复付费/已付费但是还是没法观看

## 输出 false 的场景
- 对话没有涉及来直播间听课相关话题
- 客户由于个人有事无法参加直播
- 客户由于个人手机问题无法进入直播（不属于技术问题）
- 客户表示直播课程卡顿/网络不好（不属于技术问题）
- 客户表示无法使用电脑听课（不属于技术问题）
- 客户无法进入冥想状态
- 麦子老师已经给客户发送过[上课小程序]
- 客户的问题被解决
- 客户发送图片显示正在参加墨尔冥想的线上直播课程

请根据上述指令进行推理，先输出理由到 <reason></reason> 标签中，然后将结果输出到 <answer></answer> 标签中（仅包含结果true/false）`,
      { meta: { chat_id: chat_id, promptName: 'cant_enter_live_check' } })
      sendMiniProgram = XMLHelper.extractContent(res, 'answer')
    }

    if (sendMiniProgram === 'true') {
      try {
        await HumanTransfer.transfer(chat_id, user_id, HumanTransferType.SendMiniProgram)
        const phoneNumber = await DataService.findPhoneNumber(user_id)
        const messageTemplate = '给咱们发一个小程序哈，咱们一定要用下单手机号登录哦'
        const ai_msg = phoneNumber ? `${messageTemplate}：${phoneNumber}` : messageTemplate
        const info_msg = '点击底部「学习」，就可以看到咱们5天入门营的卡片了，点进去就可以了。后续几天的课程都在这里哈，到直播时间就能看到啦！'

        let courseNo = await DataService.getCourseNoByChatId(chat_id)
        if (!courseNo) {
          logger.warn({ message: `课程号未找到 ${chat_id}` })
          courseNo =  DataService.getNextWeekCourseNo()
        }
        const courseInfo = await DataService.getCourseInfoByCourseNo(courseNo)
        const courseSku = courseInfo?.sku || ''
        const page_path = courseSku
          ? `pages/course/play.html?sku=${courseSku}`
          : 'pages/meditation/index.html?pid=781966&_um_ssrc=oGloS5MStt_0tPXvRc_bHFazogA8,oGloS5HSQV_bLwY0H9pkQUP_ozcQ&_um_sts=1733379864157'
        await MessageSender.sendById({ user_id: user_id, chat_id: chat_id, ai_msg: ai_msg })
        await sleep(3000)
        await MessageSender.sendById({
          user_id: user_id,
          chat_id: chat_id,
          ai_msg: '[上课小程序]',
          send_msg: {
            type: IWecomMsgType.MiniProgram,
            appId: 'gh_7e79cc9a6e81@app',
            description: '5天冥想入门营｜正念调频',
            pagePath: page_path,
            thumbUrl: 'https://workmate.s3.cn-northwest-1.amazonaws.com.cn/link_msg/a0afaf91-2e11-425b-a5b3-322d92ada927/fd0a950a-92e3-4307-8e64-6baefc16413c.jpg',
            title: '墨尔冥想',
            username: 'wxb944dac25d627482',
            iconUrl: 'https://mmbiz.qpic.cn/mmbiz_png/ELJnCVnUFFicdsWiaU24SEXic4zzgXAEJIR8yMwEpoeNZibulErNbicIiaibzVQkApzQt77uZyWia5xic4ZaD4Jt3P4EoHA/640?wx_fmt=png&wxfrom=200'
          }
        })
        await sleep(3000)
        await MessageSender.sendById({ user_id: user_id, chat_id: chat_id, ai_msg: info_msg })
        return MoerNode.Dummy
      } catch (error) {
        await HumanTransfer.transfer(chat_id, user_id, HumanTransferType.SendMiniProgram)
        logger.error('客户无法进入直播间，但转人工失败:', error)
      }
    }
    return null
  }

  private static async checkCourseComplete(chat_id: string, user_id: string, userMessage: string, round_id: string): Promise<MoerNode| null> {
    const currentTime = await DataService.getCurrentTime(chat_id)
    const finishTag = ['完成', '看完', '听完', '补了', '补完', '看了', '有看', '已听', '来了']
    if (isScheduleTimeBefore(currentTime, { is_course_week: true, day: 1, time: '20:00:00' })) { // 第一天上课前 检查客户是否说自己完成小课堂
      try {
        // 检查完成了，看完了，等字段
        if (!ChatStateStore.getFlags(chat_id).is_send_pre_course_completion_gift && finishTag.some((tag) => userMessage.includes(tag))) {
          await new CheckPreCourseCompletionTask().sendPreCourseCompleteGift(user_id, chat_id)
          await DataService.updateTags(user_id, '客户说完成小讲堂了')
        } else if (!ChatStateStore.getFlags(chat_id).is_send_pre_course_completion_gift && await this.checkIsListenedPreCourse(chat_id, userMessage, round_id)) {
          await new CheckPreCourseCompletionTask().sendPreCourseCompleteGift(user_id, chat_id)
          await DataService.updateTags(user_id, '客户说完成小讲堂了')
        }
      } catch (e) {
        logger.error(e)
      }
      return null
    } else if (isScheduleTimeBefore(currentTime, { is_course_week: true, day: 2, time: '20:00:00' }) && finishTag.some((tag) => userMessage.includes(tag))) { // day2上课前
      if (!await DataService.isCompletedCourse(chat_id, { day: 1, is_recording: true }) && !await DataService.isCompletedCourse(chat_id, { day: 1 })) {
        ChatStateStore.update(chat_id, {
          state:{
            is_complete_day1_course_recording: true
          }
        })
        await DataService.updateTags(user_id, '客户说完成第一课了')
      }
    } else if (isScheduleTimeBefore(currentTime, { is_course_week: true, day: 3, time: '20:00:00' }) && finishTag.some((tag) => userMessage.includes(tag))) { //  day3 上课前
      if (!await DataService.isCompletedCourse(chat_id, { day: 2, is_recording: true }) && !await DataService.isCompletedCourse(chat_id, { day: 2 })) {
        ChatStateStore.update(chat_id, {
          state:{
            is_complete_day2_course_recording: true
          }
        })
        await DataService.updateTags(user_id, '客户说完成第二课了')
      }
    } else if (isScheduleTimeBefore(currentTime, { is_course_week: true, day: 4, time: '20:00:00' }) && finishTag.some((tag) => userMessage.includes(tag))) { //  day4 上课前
      if (!await DataService.isCompletedCourse(chat_id, { day: 3, is_recording: true }) && !await DataService.isCompletedCourse(chat_id, { day: 3 })) {
        ChatStateStore.update(chat_id, {
          state:{
            is_complete_day3_course_recording: true
          }
        })
        await DataService.updateTags(user_id, '客户说完成第三课了')
      }
    }
    return null
  }

  // 客户索要周卡
  private static async checkCouponCondition(chat_id: string, user_id: string, userMessage: string): Promise<MoerNode | null> {
    const couponNode = 'checkCoupon'

    if (userMessage.includes('周卡')) {
      // 检查是否满足发送条件
      const isCompleteClass3 = await DataService.isCompletedCourse(chat_id, { day: 3 })
      if (isCompleteClass3) {
        ChatStateStore.increNodeCount(chat_id, couponNode)
        await HumanTransfer.transfer(chat_id, user_id, HumanTransferType.WeeklyCard)
      } else {
        await MessageSender.sendById({
          user_id: user_id,
          chat_id: chat_id,
          ai_msg: '咱们需要完成前三节课才能领取哦，老师这边看到咱们还有课程没完成的[愉快]，看完回放再来找老师领啦'
        })
      }
    }
    return null
  }

  // 分类路由
  private static async routeByCategory(chat_id: string, user_id: string, userMessage: string, round_id: string): Promise<MoerNode | null> {
    const category = await Router.classify(userMessage, chat_id, round_id)
    const currentTime = await DataService.getCurrentTime(chat_id)

    if (category === 1) {
      return MoerNode.EnergyTestAnalyze
    } else if (category === 2 && currentTime.is_course_week && isScheduleTimeAfter(currentTime, { is_course_week: true, day: 2, time: '20:50:00' })) {
      return MoerNode.WealthOrchardAnalyze
    } else if (category === 3) {
      return MoerNode.RespondCourseDate
    } else if (category === 4) {
      return MoerNode.SendFile
    } else if (category === 7) {  // 看课问题
      await this.SendMiniProgramNode(chat_id, user_id)
    } else if (category === 8) {  // 异常问题
      await HumanTransfer.transfer(chat_id, user_id, HumanTransferType.ProblemSolving, 'onlyNotify', `客户：${userMessage}`)
    } else if (category === 9) {  // 人工处理
      await HumanTransfer.transfer(chat_id, user_id, HumanTransferType.ProblemSolving, true, `客户：${userMessage}`)
      return MoerNode.DummyEnd
    }
    return MoerNode.Dummy
  }

  public static async classify(userMessage: string, chat_id: string, round_id: string) {
    const routerPrompt = await getPrompt('router')
    const routingNodes = `1. 能量测评：此节点涉及能量测试。客户应该点击能量测试链接，完成测试后会有一个分数。一般客户会有对这个分值的疑惑，前面我们也允诺对这个测评结果进行解读。只有当客户明确提到能量测评或能量分数时，才将输入分类到此节点
  - 例如："老师，我测完了，328分", "60分，怎么办，老师可以帮忙看看怎么回事吗？", "435分，老师可以帮忙解读下吗？"

2. 财富果园：客户输入为财富果园的景象，景象包括果园里树木和大门，围栏，秋天的动作，四季的变换循环景色等。只有当客户明确提到他们想象的果园中的具体画面时，才将输入分类到此节点，反之则不属于这个节点
  - 例如："没有大门，小冠木围栏，主树不清晰，没有看到结果，四季常青", "灰色钢铁门，没有围栏，苹果树很多，最大果树在中间，果园结了很多苹果，整个果园只有我一个人爬树上摘果，四季变化没看到，睡着了"
  - 注意：如果客户只让解读，没有给画面，则不属于这个节点

3. 课程时间查询：客户明确询问课程的具体上课时间、日期或者课程时间安排相关的问题或者质疑，包括询问某节课什么时候开始、包含特定内容的课什么时候开始，距离上课还有多久等时间相关的问题
  - 例如："第一节课什么时候上？", "今晚几点开始？", "这个课程是明天上吗？", "沉浸式秒睡不是明天上吗？", "不是下周才开营吗", "这不是明天学吗？", "今晚播啥", "明天直播内容是什么"等
  - 注意：只有当问题明确包含具体时间元素时，才属于这个节点。如果问题询问课程形式、内容或一般性安排，但不涉及具体时间，不属于这个节点
  - 注意：如果客户只是在陈述自己与时间相关的情况或可用性（例如，告知自己何时有空/没空），而不是在提出疑问，则不属于此节点。
  - 反例: "我可以今晚听课", "我明天有事，参加不了", "我已经准备好今晚听课了"

4. 发送课程资料：客户输入为【发送资料/奖励/完课礼/课程回放请求/音频/视频/思维导图/课程笔记/身心对照表/海浪冥想/沉浸式秒睡/α波纯音乐/红靴子音频/路易斯/小课堂链接】或其他冥想相关资料时，判断前请不要进行过度推理
  - 例如：“请问有课程回放吗”“有回放吗”“要回看”“能否补听”“【图片】完成第二课后获得的奖励”“老师说给一个礼物是什么”“领表”“现在没法看回放吗？”“对照表是什么？”“视频里说有一个表格”“身心对照卡点表何时给我”
  - 注意：若客户询问“回放有效期”，或者说“已看完回放”，则不是索要回放行为，不属于这个节点，其他资料同理，客户索要“直播间链接”也不属于这个节点`

    const output = await LLM.predict(
      routerPrompt, {
        model: 'gpt-5-mini',
        responseJSON: true,
        meta: {
          promptName: 'router',
          chat_id: chat_id,
          round_id: round_id,
        } }, {
        routingNodes: routingNodes,
        customerMessage: userMessage,
      })
    let answer: number = 0
    try {
      const parsedOutput = JSON.parse(output)
      answer = parsedOutput.answer
    } catch (error) {
      logger.error('Router 解析 JSON 失败:', error)
    }
    return answer || 0
  }

  public static async checkIsRepliedSurvey(chat_id: string, userMessage: string) {
    const isRepliedSurvey = await this.isRepliedSurvey(userMessage)
    if (!isRepliedSurvey) return ''

    const userMessageCount = await ChatHistoryService.getUserMessageCount(chat_id)
    if (userMessageCount > 9)  return ''

    // 提取数字转为对应文字
    const extractedTexts = StringHelper.extractNumbersToText(userMessage)

    // 如果成功提取到文字描述，可以在这里进行后续处理
    if (extractedTexts.length > 0) {
      return extractedTexts.join(',')
    }

    return ''
  }

  private static async isRepliedSurvey(userMessage: string) {
    // 检查是否是手机号
    if (RegexHelper.extractPhoneNumber(userMessage)) {
      return false
    }

    /** 把常用中文数字映射成阿拉伯数字 */
    const chineseMap: Record<string, number> = {
      '一': 1,  '二': 2,  '三': 3,  '四': 4,
      '五': 5,  '六': 6,  '七': 7,  '八': 8,  '九': 9,
      '十': 10, '十一': 11, '十二': 12, '十三': 13,
    }

    const numbers: number[] = []

    /* 1️⃣ 抓中文数字——先匹配长度较长的“十一”“十二”“十三”，再匹配其余 */
    const cnRegex = /十一|十二|十三|十|[一二三四五六七八九]/g;
    (userMessage.match(cnRegex) || []).forEach((cn) => numbers.push(chineseMap[cn]))

    /* 2️⃣ 抓阿拉伯数字 1–13——用重叠匹配把 8910… 拆成 8 9 10… */
    const arRegex = /1[0-3]|[1-9]/g;
    (userMessage.match(arRegex) || []).forEach((num) => numbers.push(parseInt(num, 10)))

    /* 3️⃣ 判断三大区间是否各出现至少一次 */
    const hasRole   = numbers.some((n) => n >= 1 && n <= 4)   // 生活角色
    const hasExp    = numbers.some((n) => n >= 5 && n <= 7)   // 冥想经验
    const hasTopic  = numbers.some((n) => n >= 8 && n <= 13)  // 人生议题

    return (hasRole ? 1 : 0) + (hasExp ? 1 : 0) + (hasTopic ? 1 : 0) >= 2
  }

  private static async checkIsListenedPreCourse(chat_id: string, userMessage: string, round_id: string) {
    const matchPattern = [
      '听过', '听过了', '听完', '听完了', '听了几次', '听几次', '已经听',
      '学过', '学过了', '学完', '学完了', '已经学',
      '看过', '看过了', '看完', '看完了', '已经看'
    ]

    if (matchPattern.some((pattern) => userMessage.includes(pattern))) {
      const chatHistory = await ChatHistoryService.getRecentConversations(chat_id, 2)

      const prompt = SystemMessagePromptTemplate.fromTemplate(`You are tasked with analyzing a conversation to determine if a user has indicated they have completed or watched a small lecture (called "小讲堂" in Chinese). This is an important task to track student progress.
    
I will provide you with a conversation between a teacher and a student. Your job is to analyze whether the student's response indicates they have already watched or completed the small lecture.

When determining this, look for phrases that clearly indicate completion such as:
- "看完了" (watched/finished)
- "学完了" (learned/completed)
- "看过了" (have watched)
- "学过了" (have learned)
- "听过了" (have listened)
- Phrases that directly state they've already completed it
- Similar expressions that confirm completion

First, carefully analyze the conversation in <thinking> tags, identifying key phrases that indicate completion status. Then return your conclusion as either true (they have completed it) or false (they have not completed it) inside <result> tags.

Important: Only determine true if the user specifically indicates they've completed the small lecture (小讲堂). If they say they've watched/completed something else, or if it's ambiguous what they've completed, return false.

Here are some examples:

Example 1:
"麦子老师：建议您先花10分钟看下唐宁老师的小讲堂，里面有详细介绍后续课程安排，还能提前体验一次「海浪冥想」，对后面学习特别重要哦～记得看完告诉我一声，我这边送您电子版《冥想练习指南》当小奖励哈～
客户：已经学过了哦"

<thinking>
In this conversation, the teacher recommends that the user spend 10 minutes watching Teacher Tang Ning's small lecture (小讲堂). The user responds with "已经学过了哦" which means "I have already learned/studied it." This clearly indicates the user has completed the small lecture.
</thinking>
<result>
true
</result>

Example 2:
"麦子老师：哈哈哈催了同学好几次了，我都不好意思了，你的小讲堂还没听哦~全班就剩下你了。:https:/t.meihao.com/KKZd
客户：我听几次了"

<thinking>
The teacher is reminding the user that they haven't listened to the small lecture yet (小讲堂还没听). However, the user responds with "我听几次了" meaning "I've listened to it several times." This clearly contradicts the teacher's statement and indicates the user has completed the small lecture multiple times.
</thinking>
<result>
true
</result>

Example 3:
"麦子老师：你最近看过《泰坦尼克号》么
客户：这个我看过了"

<thinking>
In this conversation, the teacher is asking if the user has watched the movie "Titanic" recently. The user responds with "这个我看过了" which means "I have watched this." However, this is about watching the movie Titanic, not about completing the small lecture (小讲堂). Therefore, the user has not indicated completion of the small lecture.
</thinking>
<result>
false
</result>

Now, analyze the following conversation:

<conversation>
{chat_history}
</conversation>`)

      const llmRes = await LLM.predict(prompt, { meta: { chat_id, round_id } },  { chat_history: ChatHistoryService.formatHistoryHelper(chatHistory) })

      return await LLMXMLHelper.extractBooleanAnswer(llmRes, { tagName: 'result', trueFlag: 'true', falseFlag: 'false' })
    }

    return false
  }

  //上课期间，检查客户是否已经说自己在直播间里了
  private static async checkIsInCourse(chat_id: string, userMessage: string, round_id: string) {
    const currentTime = await DataService.getCurrentTime(chat_id)
    if (!await DataService.isWithinClassTime(currentTime)) {
      return
    }
    const checkIsInCoursePrompt = SystemMessagePromptTemplate.fromTemplate(`请分析客户的消息，判断客户是否已经在课程直播间中参与学习。

客户消息：
{userMessage}

格式要求：
请将结果输出到<result></result>中，true表示客户已经在直播间课程中，false表示客户不在`)

    const llmRes = await LLM.predict(checkIsInCoursePrompt, { meta: { chat_id: chat_id, round_id: round_id } }, { userMessage })
    const res = await LLMXMLHelper.extractBooleanAnswer(llmRes, { tagName: 'result', trueFlag: 'true', falseFlag: 'false' })

    const dayStatusMap = {
      1: 'is_in_day1_class',
      2: 'is_in_day2_class',
      3: 'is_in_day3_class',
      4: 'is_in_day4_class'
    }
    const status = dayStatusMap [currentTime.day]

    if (res) {
      ChatStateStore.update(chat_id, {
        state:{
          [status]: true
        }
      })
    }
  }

}