import { DataService } from '../../../../getter/getData'
import { listSOPByChatId } from '../task_starter'
import { CacheDecorator } from '../../../../../../lib/cache/cache'
import { ChatDB } from '../../../../database/chat'
import { getTaskList } from '../task/getTaskList'
import { calTaskTime, IScheduleTime } from '../../../schedule/creat_schedule_task'
import { ScheduleTask } from '../../../schedule/schedule'
import { getUserId } from '../../../../../../config/chat_id'
import { MoerAPI } from '../../../../../../model/moer_api/moer'
import { sleep } from '../../../../../../lib/schedule/schedule'
import { Queue } from 'bullmq'
import { RedisDB } from '../../../../../../model/redis/redis'
import { loadConfigByWxId } from '../../../../../../../test/tools/load_config'
import { Config } from '../../../../../../config/config'

describe('Test', function () {
  beforeAll(() => {

  })

  it('测试', async () => {
    const queue = new Queue('moer-general-sop-1688855025632783', {
      connection: RedisDB.getInstance(),
    })

    const workers = await queue.getWorkers()

    console.log(`当前有 ${workers.length} 个 Worker 正在监听此队列`)
    // 如果需要详细信息：
    workers.forEach((w) => {
      console.log(`• Worker PID: ${w.pid}, 客户端 ID: ${w.client}, 名称: ${w.name}`)
    })
  }, 60000)


  it('12312', async () => {
    // const queue = new Queue(`moer-general-sop-1688856322643146`, {
    //   connection: RedisDB.getInstance(),
    // })
    //
    // console.log(JSON.stringify(await queue.getDelayed(), null, 4))

    Config.setting.wechatConfig = await loadConfigByWxId('1688855025632783')

    const sopL = await listSOPByChatId('123')

    console.log(sopL.length)
    // console.log(JSON.stringify(await DataService.getCourseStartTime('7881300846030208_1688854546332791'), null, 4))
  }, 60000)

  it('fk', async () => {
    const chat = await ChatDB.getById('7881302231951646_1688855548631328')
    console.log(JSON.stringify(chat, null, 4))
  }, 60000)


  it('关掉所有人的SOP', async () => {
    const chats = await DataService.getChatsByCourseNo(59)
    await Promise.all(chats.map((chat) => ChatDB.setStopGroupPush(chat.id, false)))
  }, 1E8)

  it('获取 64期 重复 SOP的客户', async () => {
    const chats = await DataService.getChatsByCourseNo(64)

    // const jobs = await listGeneralSopTasksByChatId(chatId, botId)
    // if (jobs.length > 0) {
    //   return
    // }

  }, 60000)

  it('清空所有队列', async () => {

  }, 60000)

  it('should pass', async () => {
    // 55期未付款客户，添加任务
    const chats = await DataService.getChatsByCourseNo(64)
    const BATCH_SIZE = 50 // 每批处理50个客户

    MoerAPI.getCurrentCourseInfo =  CacheDecorator.decorateAsync(MoerAPI.getCurrentCourseInfo)
    DataService.getCourseStartTime = CacheDecorator.decorateAsync(DataService.getCourseStartTime)

    await MoerAPI.getCurrentCourseInfo(64)
    await MoerAPI.getCurrentCourseInfo(64)

    /**
     * 将数组按指定大小分组
     * @param array 原始数组
     * @param batchSize 分组大小
     */
    function batchArray<T>(array: T[], batchSize: number): T[][] {
      const batches: T[][] = []
      for (let i = 0; i < array.length; i += batchSize) {
        batches.push(array.slice(i, i + batchSize))
      }
      return batches
    }

    /**
     * 批量处理聊天记录
     */
    async function processChatsBatch(chatBatch: any[]) {
      chatBatch = chatBatch.filter((chat) => {
        // chat.id 不能包含字母
        return /^[0-9_]+$/.test(chat.id)
      })

      // 为未付款客户添加任务
      const addTaskPromises = chatBatch.map((chat) => {
        const userId = getUserId(chat.id)
        return addTask(userId, chat.id, chat.wx_id)
      })

      await Promise.all(addTaskPromises)
      return chatBatch.length
    }

    async function addTask(userId: string, chatId: string, botId: string) {
      const tasks = await getTaskList(userId, chatId)
      // 使用Promise.all进行并发操作
      await Promise.all(tasks.map(async (task) => {
        task.sendTime = await calTaskTime(task.scheduleTime as IScheduleTime, task.chatId)
      }))

      await ScheduleTask.addTasks(`moer-general-sop-${botId}`, tasks)
      //
      // const afterAdded = await listGeneralSopTasksByChatId(chatId, botId)
      // if (afterAdded.length === 0) {
      //   logger.error(`WTF, 添加任务失败: ${chatId}`)
      // }
    }

    // 将所有聊天记录分批处理
    async function processAllChatsInBatches() {
      const chatBatches = batchArray(chats, BATCH_SIZE)
      let processedCount = 0

      console.log(`总共 ${chats.length} 个客户，分成 ${chatBatches.length} 批处理`)

      for (let i = 0; i < chatBatches.length; i++) {
        console.log(`开始处理第 ${i + 1}/${chatBatches.length} 批...`)
        const batchCount = await processChatsBatch(chatBatches[i])
        processedCount += batchCount
        console.log(`第 ${i + 1} 批已完成，此批添加了 ${batchCount} 个未付款客户的任务`)
      }

      console.log(`处理完成，总共 ${chats.length} 个客户中，为 ${processedCount} 个未付款客户添加了任务`)
      return processedCount
    }

    // 执行批量处理
    await processAllChatsInBatches()

    await sleep(60 * 1000)
  }, 1E8)

  it('next course', async () => {
    console.log(JSON.stringify(await MoerAPI.getCurrentCourseInfo(84), null, 4))
  }, 30000)
})