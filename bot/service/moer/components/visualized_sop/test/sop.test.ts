import { MoerVisualizedSopProcessor } from '../moer_visualized_sop_processor'
import { Config } from '../../../../../config/config'
import { loadConfigByAccountName } from '../../../../../../test/tools/load_config'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    Config.setting.wechatConfig = await loadConfigByAccountName('moer3')
    Config.setting.localTest = false


    await new MoerVisualizedSopProcessor().handleSopBySopId('****************_1688856322643146', '****************', '68d35b244ec8860c86546b96')
  })
})