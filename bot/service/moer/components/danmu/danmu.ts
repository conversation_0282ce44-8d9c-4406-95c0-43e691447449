import { DataService } from '../../getter/getData'
import { <PERSON>, Queue, Worker } from 'bullmq'
import { calTaskTime, IScheduleTime } from '../schedule/creat_schedule_task'
import dayjs from 'dayjs'
import logger from '../../../../model/logger/logger'
import { RedisDB } from '../../../../model/redis/redis'
import { ITask } from '../schedule/type'
import { TaskName } from '../flow/schedule/type'
import { ScheduleTask } from '../schedule/schedule'
import { PrismaMongoClient } from '../../../../model/mongodb/prisma'
import { DanmuAnalysisResult, DanmuAnalysisResultListToChatHistoryWithRoleAndDate, DanmuAnalyzer } from './danmu_analyzer'
import { ExtractUserSlotsV2 } from '../flow/helper/slotsExtract'
import { ChatStateStore, ChatStatStoreManager } from '../../storage/chat_state_store'
import { getUserId } from '../../../../config/chat_id'
import { EventTracker, IEventType } from '../../../../model/logger/data_driven'
import { DanmuMemorySummaryPrompt } from '../../prompt/moer/userMemorySummary'
import { LLM } from '../../../../lib/ai/llm/LLM'
import { MemoryStore } from '../memory/memory_store'


/**
 * 弹幕的处理逻辑
 */
export class DanmuHelper {
  /**
   * 拉取弹幕，将弹幕存库。
   * 在处理完最后一次弹幕任务后，清空任务列表，添加新任务
   */
  public static async processPullDanmu() {
    new Worker('pullDanmu', async (job: Job) => { // 抢占式 worker 哪个拿到，哪个执行
      logger.log('拉取弹幕...')
      const scheduleTime = job.data.scheduleTime as  IScheduleTime
      try {
        const currentCourseNo = DataService.getCurrentWeekCourseNo()
        // 获取 直播 ID
        const courseInfo = await DataService.getCourseInfoByCourseNo(currentCourseNo)
        const liveId = courseInfo.resource.find((resource) => resource.day === scheduleTime.day)?.liveId as number

        if (!liveId) {
          logger.warn('获取直播 ID 失败')
          return
        }

        // 批量拉取弹幕存储到数据库
        const formattedDate = dayjs().format('YYYY-MM-DD')
        await DataService.pullLiveDanmuAndStore(liveId.toString(10), formattedDate, formattedDate, job.data.courseNo, scheduleTime.day)

        await DanmuHelper.processDanmus(job.data.courseNo, liveId.toString(10), scheduleTime.day)
      } catch (e) {
        logger.error('弹幕处理失败', JSON.stringify(scheduleTime), e)
      }

      // 如果是最后一天，要添加下一次的任务
      if (scheduleTime.day === 4) {
        await DanmuHelper.addNextWeekPullDanmuTasks()
      }
    }, {
      connection: RedisDB.getInstance()
    }).on('error', (e) => {
      logger.error('弹幕拉取 worker 错误', e)
    })
  }

  public static async addNextWeekPullDanmuTasks() {
    const queue = new Queue('pullDanmu', {
      connection: RedisDB.getInstance()
    })

    const hasTasks = await queue.count()
    if (hasTasks > 0) {
      await queue.obliterate({ force: true })
    }

    // 每天下课时候，拉取一次弹幕，拉取过了就不再拉取
    const tasks: ITask[] = []

    tasks.push({
      name: TaskName.PullDanmu,
      chatId: '',
      userId: '',
      scheduleTime:  {
        is_course_week: true,
        day: 1,
        time: '21:45:00'
      },
      courseNo: DataService.getNextWeekCourseNo()
    })

    tasks.push({
      name: TaskName.PullDanmu,
      chatId: '',
      userId: '',
      scheduleTime:  {
        is_course_week: true,
        day: 2,
        time: '21:15:00'
      },
      courseNo: DataService.getNextWeekCourseNo()
    })

    tasks.push({
      name: TaskName.PullDanmu,
      chatId: '',
      userId: '',
      scheduleTime:  {
        is_course_week: true,
        day: 3,
        time: '22:20:00'
      },
      courseNo: DataService.getNextWeekCourseNo()
    })

    tasks.push({
      name: TaskName.PullDanmu,
      chatId: '',
      userId: '',
      scheduleTime:  {
        is_course_week: true,
        day: 4,
        time: '22:20:00'
      },
      courseNo: DataService.getNextWeekCourseNo()
    })

    for (const task of tasks) {
      task.sendTime = await calTaskTime(task.scheduleTime as IScheduleTime, task.chatId, true)
    }

    await ScheduleTask.addTasks('pullDanmu', tasks)
  }


  /**
 * 首次拉取弹幕，冷启动，添加任务，注意：调用一次之后就不需要再次执行，因为任务本身后续会自动添加任务
 */
  public static async startPullDanmuTask() {
    const queue = new Queue('pullDanmu', {
      connection: RedisDB.getInstance()
    })

    // const hasTasks = await queue.getDelayedCount()
    // if (hasTasks > 0) {
    //   return
    // }

    // 从 开课时间开始， 每天 xx 点，拉取一次弹幕，拉取过了就不再拉取
    // 1. 获取课程信息
    const tasks: ITask[] = []

    // Day1
    tasks.push({
      name: TaskName.PullDanmu,
      chatId: '',
      userId: '',
      scheduleTime:  {
        is_course_week: true,
        day: 1,
        time: '21:45:00'
      },
      courseNo: 47
    })

    tasks.push({
      name: TaskName.PullDanmu,
      chatId: '',
      userId: '',
      scheduleTime:  {
        is_course_week: true,
        day: 2,
        time: '21:15:00'
      },
      courseNo: 47
    })

    tasks.push({
      name: TaskName.PullDanmu,
      chatId: '',
      userId: '',
      scheduleTime:  {
        is_course_week: true,
        day: 3,
        time: '22:20:00'
      },
      courseNo: 47
    })

    tasks.push({
      name: TaskName.PullDanmu,
      chatId: '',
      userId: '',
      scheduleTime:  {
        is_course_week: true,
        day: 4,
        time: '22:20:00'
      },
      courseNo: 47
    })

    for (const task of tasks) {
      task.sendTime = await calTaskTime(task.scheduleTime as IScheduleTime, task.chatId, true)
    }

    await ScheduleTask.addTasks('pullDanmu', tasks)
  }

  public static async processDanmus(courseNo:number, liveId: string,  day: number) {
    const chats = await  PrismaMongoClient.getInstance().chat.findMany({
      where: {
        course_no: courseNo,
      }
    })

    const qaMap = new Map<string, DanmuAnalysisResult[]>() // chat_id -> qas

    for (const chat of chats) {
      if (!chat.moer_id)
        continue

      const qas = await DanmuAnalyzer.analyzeDanmu(chat.moer_id, liveId, day)

      if (!qas.length) {
        continue
      }

      qaMap.set(chat.id, qas)
    }


    // 处理 QA 对
    for (const [chatId, qas] of qaMap.entries()) {
      // 更新槽位信息
      await this.updateUserSlots(chatId, qas, day)
      // 更新 Memory
      await this.updateMemory(chatId, qas, day)
    }
  }

  private static async updateUserSlots(chatId: string, qas: DanmuAnalysisResult[], day: number) {
    if (!qas.length)
      return

    await ChatStatStoreManager.initState(chatId)

    // 合并槽位
    await ExtractUserSlotsV2.extractUserSlotsFromChatHistory(DanmuAnalysisResultListToChatHistoryWithRoleAndDate(qas), chatId, true,  { chat_id:chatId })

    // Day3, Day4 增加一下 新的意向槽位
    if (day === 3 || day === 4) {
      if (qas.some((qa) => qa.answers.some((answer) => ['想学', '想买', '想了解', '已拍', '已买'].includes(answer)))) {
        ChatStateStore.update(chatId, { userSlots: { is_willing_to_purchase: true } })

        await DataService.saveChat(chatId, getUserId(chatId))

        // 埋点
        EventTracker.track(chatId, IEventType.LiveStreamWillingToBuy, { danmu: JSON.stringify(qas) })
      }
    }
  }

  private static async updateMemory(chatId: string, qas: DanmuAnalysisResult[], day: number) {
    if (!qas.length)
      return

    const llm = new LLM({ model: 'gpt-5-mini', meta: { promptName: DanmuMemorySummaryPrompt.name } })

    const formattedSummaryPrompt = await DanmuMemorySummaryPrompt.format(JSON.stringify(qas))
    let summarizedMemory = await llm.predict(formattedSummaryPrompt)

    if (summarizedMemory.includes('null')) {
      return
    }

    // 删除对于总结的总结
    summarizedMemory = summarizedMemory.split('整体来看')[0]

    await MemoryStore.addMemoryToVectorDB(chatId, summarizedMemory, new Date().toISOString())
  }
}