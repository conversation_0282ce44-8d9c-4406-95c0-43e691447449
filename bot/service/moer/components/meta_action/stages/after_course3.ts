import { MetaActionComponent } from '../meta_action_component'
import { Promise } from 'mongoose'
import { IActionInfo, PostAction } from './post_action'
import { DataService } from '../../../getter/getData'
import { isScheduleTimeAfter } from '../../schedule/creat_schedule_task'
import { MetaActions, ThinkPrompt } from '../context'

export class AfterCourse3 extends MetaActionComponent {
  async isStageActive(chatId: string): Promise<boolean> {
    const currentTime = await DataService.getCurrentTime(chatId)
    const afterCourse3Sales = isScheduleTimeAfter(currentTime, { is_course_week: true, day: 3, time: '21:00:00' })
    return Promise.resolve(afterCourse3Sales)
  }

  getAction(chatId: string, roundId: string): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    const actionMap: Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> = {
      '发起报名邀约': PostAction.sendInvitation,
      '发送学员案例': PostAction.sendCaseImage,
    }
    return Promise.resolve(actionMap)
  }

  getAdditionInfo(chatId: string): Promise<string> {
    return Promise.resolve('')
  }

  getMetaAction(chatId: string): Promise<Record<string, string>> {
    return Promise.resolve(MetaActions.afterCourse3)
  }

  getThinkPrompt(chatId: string): Promise<string> {
    return Promise.resolve(ThinkPrompt.afterCourse3)
  }

  prepareActivation(chatId: string, roundId: string): Promise<void> {
    return Promise.resolve(undefined)
  }
}