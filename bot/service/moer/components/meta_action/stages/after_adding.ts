import { MetaActionComponent } from '../meta_action_component'
import { Promise } from 'mongoose'
import { ChatStateStore } from '../../../storage/chat_state_store'
import { FreeTalk } from '../../agent/freetalk'
import { IActionInfo, PostAction } from './post_action'
import { MetaActions, ThinkPrompt } from '../context'

export class AfterAdding extends MetaActionComponent {
  isStageActive(chatId: string): Promise<boolean> {
    const nodeCount = ChatStateStore.getNodeCount(chatId, FreeTalk.name)
    return Promise.resolve(nodeCount <= 15)
  }
  getAction(chatId: string, roundId: string): Promise<Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> | null> {
    const actionMap: Record<string, (chat_id: string, round_id: string) => Promise<IActionInfo>> = {
      '提醒完成小讲堂': PostAction.sendPreCourseLink,
    }
    return Promise.resolve(actionMap)
  }

  getAdditionInfo(chatId: string): Promise<string> {
    return Promise.resolve('')
  }

  getMetaAction(chatId: string): Promise<Record<string, string>> {
    return Promise.resolve(MetaActions.afterAdding)
  }

  getThinkPrompt(chatId: string): Promise<string> {
    return Promise.resolve(ThinkPrompt.afterAdding)
  }

  prepareActivation(chatId: string, roundId: string): Promise<void> {
    return Promise.resolve(undefined)
  }
}