// import { ChatHistoryService, IDBBaseMessage } from '../chat_history/chat_history'
// import { LLM } from '../../../../lib/ai/llm/LLM'
// import logger from '../../../../model/logger/logger'
// import { ChatStateStore } from '../../storage/chat_state_store'
//
// import ElasticSearchService from '../../../../model/elastic_search/elastic_search'
// import { AzureOpenAIEmbedding } from '../../../../lib/ai/llm/openai_embedding'
// import { PrismaMongoClient } from '../../../../model/mongodb/prisma'
// import * as path from 'path'
// import { FileHelper } from '../../../../lib/file'
// import { MemorySummaryOnline } from '../memory/memorySummary'
// import { MemorySummaryPrompt } from '../../prompt/moer/userMemorySummary'
// import { ElasticClientArgs, ElasticVectorSearch } from '@langchain/community/vectorstores/elasticsearch'
// import { Client, ClientOptions } from '@elastic/elasticsearch'
// import { Document } from 'langchain/document'
// import { Config } from '../../../../config/config'
//
//
// // 每5轮一次，将该客户近10轮聊天与历史memory送入【总结LLM】，让模型判断是否需要归纳一条新的memory入库，
// // 若不需要，则不处理，反之，将归纳好的memory入库
// interface ChatSessionGroup {
//   content: string[]
//   lastMessageTimestamp: Date
// }
// interface SummarizedMemory {
//   summary: string
//   timestamp: Date
// }
// interface FormattedSummarizedMemory {
//   chat_id: string
//   summary: string
//   timestamp: string
// }
// const clientConfig: ClientOptions = {
//   node: Config.setting.elasticSearch.url,
//   auth: {
//     username: Config.setting.elasticSearch.username,
//     password: Config.setting.elasticSearch.password,
//   },
// }
// interface QA {
//   q: string
//   a: string
//   chunk: string
//   doc: string
// }
//
//
// class memorySummary {
//   public static async processFullChatHistory(chat_id: string): Promise<ChatSessionGroup[]> {
//     const fullChatHistory = await ChatHistoryService.getChatHistoryByChatId(chat_id)
//     const rounds: IDBBaseMessage[][] = []
//     let currentRound: IDBBaseMessage[] = []
//
//     // 将消息分组为轮次
//     for (const message of fullChatHistory) {
//       if (message.role === 'user' && currentRound.length > 0) {
//         // 当遇到客户消息且当前轮次不为空时，结束当前轮次
//         rounds.push(currentRound)
//         currentRound = []
//       }
//       currentRound.push(message)
//     }
//
//     // 如果最后一轮不完整,也将其添加
//     if (currentRound.length > 0) {
//       rounds.push(currentRound)
//     }
//
//     const results: ChatSessionGroup[] = []
//
//     // 每5轮处理一次，但每次处理10轮（或更少）
//     for (let i = 0; i < rounds.length; i += 5) {
//       // 获取最近的10轮（或更少，如果不足10轮）
//       const startIndex = Math.max(0, i - 4)  // 确保我们至少有5轮，但不超过10轮
//       const tenRounds = rounds.slice(startIndex, Math.min(startIndex + 10, rounds.length))
//
//       // 将10轮（或更少）的对话处理为一个string[]
//       const processedRounds = tenRounds.map((round) => {
//         // 格式化每条消息
//         const formattedMessages = round.map((message) =>
//           `${message.role}: ${message.content}`
//         )
//         // 将格式化的消息用两个换行符连接
//         return formattedMessages.join('\n\n')
//       })
//
//       // 获取最后一轮中最后一条消息的时间戳
//       const lastRound = tenRounds[tenRounds.length - 1]
//       const lastMessage = lastRound[lastRound.length - 1]
//       const lastMessageTimestamp = new Date(lastMessage.created_at)
//
//       // 将处理后的内容和时间戳添加到结果数组中
//       results.push({
//         content: processedRounds,
//         lastMessageTimestamp: lastMessageTimestamp
//       })
//     }
//
//     return results
//   }
//
//   // processChatHistoryResults是所有按轮分好的的聊天记录。把每一轮的聊天记录给到llm总结成 memory，并且从向量库中读取之前的memory，
//   // 判断是否需要归纳一条新的memory入elastic search 向量库。若不需要，则不处理，反之，将归纳好的memory入库
//
//   // 只对聊天记录进行改写
//   public static async summarizeProcessedChatHistory(processedHistory: ChatSessionGroup[]): Promise<SummarizedMemory[]> {
//     const llm = new LLM({ model: 'gpt-5-mini' })
//     const summaries: SummarizedMemory[] = []
//
//     for (const group of processedHistory) {
//       try {
//         // 使用 MemorySummaryPrompt 来格式化 prompt
//         const formattedPrompt = await MemorySummaryPrompt.format(group.content)
//
//         // 调用 LLM 进行总结
//         const summarizedMemory = await llm.predict(formattedPrompt)
//
//         // 将总结和时间戳添加到结果数组
//         summaries.push({
//           summary: summarizedMemory,
//           timestamp: group.lastMessageTimestamp
//         })
//       } catch (error) {
//         logger.error('Error in memory summarize:', error)
//         // 在出错的情况下，添加一个空总结
//         summaries.push({
//           summary: '',
//           timestamp: group.lastMessageTimestamp
//         })
//       }
//     }
//     return summaries
//   }
//   public static async generateAndFormatSummaries(chat_id: string): Promise<FormattedSummarizedMemory[]> {
//     // 获取处理过的聊天历史
//     const processedHistory: ChatSessionGroup[] = await this.processFullChatHistory(chat_id)
//
//     // 生成摘要
//     const summaries: SummarizedMemory[] = await this.summarizeProcessedChatHistory(processedHistory)
//
//     // 格式化摘要
//     const formattedSummaries: FormattedSummarizedMemory[] = summaries.map((item) => ({
//       chat_id: chat_id,
//       summary: JSON.stringify(item.summary),
//       timestamp: item.timestamp.toISOString()
//     }))
//
//     return formattedSummaries
//   }
//
//   public static async generateSummariesForChats(courseNo: number) {
//     // Fetch chat records based on course_no
//     let chats = await PrismaMongoClient.getInstance().chat.findMany({
//       where: {
//         course_no: courseNo
//       },
//     })
//
//     // Filter out unwanted chat records
//     chats = chats.filter((chat) => {
//       return ! ['哈哈哈', 'SYQ', '麦子', 'Horus', 'Tony Gu', 'god', '小仙女本仙', 'test'].includes(chat.contact.wx_name)
//     })
//
//     // Extract chat IDs
//     const chatIds = chats.map((chat) => chat.id)
//
//     // Initialize an array to hold the formatted summaries
//     const allFormattedSummaries: FormattedSummarizedMemory[] = []
//
//     // Iterate over the chat IDs and generate summaries
//     for (const chat_id of chatIds) {
//       const formattedSummaries = await memorySummary.generateAndFormatSummaries(chat_id)
//       allFormattedSummaries.push(...formattedSummaries)
//     }
//
//     const filename = `course_${courseNo}_summaries_${new Date().toISOString().split('T')[0]}.json`
//
//     // Write the summaries to a JSON file
//     const currentDir = __dirname
//
//     // Create the full path for the output file
//     const outputPath = path.join(currentDir, filename)
//
//     // Write the summaries to a JSON file
//     try {
//       await FileHelper.writeFile(outputPath, JSON.stringify(allFormattedSummaries, null, 2))
//       console.log(`Summaries have been saved to ${filename} in the current directory.`)
//     } catch (error) {
//       console.error(`Error writing file: ${error}`)
//       throw error // 重新抛出错误，以便调用者可以捕获它
//     }
//   }
//
//
//
// }
//
//
//
// describe('', () => {
//   it('测试线上插入memory', async () => {
//     const chat_id = '7881302505051440_1688857003605938'
//     ChatStateStore.update(chat_id, {
//       nodeInvokeCount: {
//         'UserMessage': 9
//       }
//     })
//     await MemorySummaryOnline.pushRecentMemoryToVectorDB(chat_id)
//   }, 3000000)
//
//   it('测试线上总结memory', async () => {
//     // 哈哈哈 7881302505051440_1688857003605938
//     // horos 7881302298050442_1688857003605938
//     // 丽 7881302192916883_1688857003605938
//     // 止语 7881301295921382_1688857003605938
//     const chat_id = '7881302505051440_1688857003605938'
//     const fullChatHistory = await ChatHistoryService.getChatHistoryByChatId(chat_id)
//     console.log(fullChatHistory.length)
//     console.log(fullChatHistory.slice(-10))
//     ChatStateStore.update(chat_id, {
//       nodeInvokeCount: {
//         'UserMessage': 9
//       }
//     })
//     const processChatHistory = await MemorySummaryOnline.processChatHistory(chat_id)
//     const processChatHistoryToString = processChatHistory.content
//     // console.log('往前10轮的记录', processChatHistory)
//     console.log('往前10轮的记录', processChatHistoryToString)
//     const previousMemory = await ElasticSearchService.memoryQuerySearch('user_memory', chat_id)
//     console.log('previousMemory', previousMemory)
//     const rewriteMemory = await MemorySummaryOnline.rewriteChatHistoryToMemory(processChatHistory, chat_id)
//     //console.log('新的 memory', rewriteMemory)
//   }, 3E8)
//
//   it('拿到所有客户的memory', async () => {
//     try {
//       const courseNo = 38
//       await memorySummary.generateSummariesForChats(courseNo)
//       console.log(`Summaries for course ${courseNo} have been generated and saved.`)
//     } catch (error) {
//       console.error('Error in generating summaries for chats:', error)
//     }
//   }, 9E8)
//
//   it('getFullChatHistory', async () => {
//     const fullChatHistory = await ChatHistoryService.getChatHistoryByChatId('7881301241917568_1688857003605938')
//     //const processChatHistoryResults = await memorySummary.processChatHistory('7881301241917568_1688857003605938')
//     const processFullChatHistoryResults = await memorySummary.processFullChatHistory('7881301241917568_1688857003605938')
//     console.log(processFullChatHistoryResults.length)
//     console.log(processFullChatHistoryResults)
//     console.log(fullChatHistory)
//   }, 30000)
//
//   it('重写记忆', async () => {
//     const processFullChatHistoryResults = await memorySummary.processFullChatHistory('7881301241917568_1688857003605938')
//     const summarizeMemoryRes = await memorySummary.summarizeProcessedChatHistory(processFullChatHistoryResults)
//     console.log(summarizeMemoryRes)
//   }, 30000000)
//
//   it('输出格式化记忆', async () => {
//     try {
//       const chat_id = '7881301295921382_1688857003605938'
//       const formattedSummaries = await memorySummary.generateAndFormatSummaries(chat_id)
//       console.log(JSON.stringify(formattedSummaries, null, 2))
//     } catch (error) {
//       console.error('Error in main function:', error)
//     }
//   }, 30000000)
//
//   it('chatId', async () => {
//     // 拉出当前期所有客户
//     // 向消息队列中添加一个任务
//     let chats = await PrismaMongoClient.getInstance().chat.findMany({
//       where: {
//         course_no: 37
//       },
//     })
//
//     chats = chats.filter((chat) => {
//       return ! [ 'Horus', 'god', '小仙女本仙', 'test'].includes(chat.contact.wx_name) //, 'Tony Gu', 'SYQ','哈哈哈','麦子',
//     })
//     console.log(chats)
//     const chatIds = chats.map((chat) => chat.id)
//
//     // 打印所有的 id
//     console.log(chatIds)
//     console.log(chatIds.length)
//   }, 60000)
//
//   it('检查 memorySearch', async () => {
//     const res = await ElasticSearchService.memoryQuerySearch('user_memory', '7881301241917568_1688857003605938')
//     console.log(res)
//   }, 30000)
//
//   // it('创建 chatID的 memory', async () => {
//   //   await ElasticSearchService.createIndex('user_memory_index', {
//   //         'properties': {
//   //           'embeddings': { 'type': 'dense_vector', index: true },
//   //           'metadata.q': { 'type': 'text', 'analyzer': 'ik_max_word', 'search_analyzer': 'ik_smart' }, // 客户的memory
//   //           'metadata.a': { 'type': 'keyword', index: true }, // 客户的 chat_id
//   //           'metadata.doc': { 'type': 'keyword' }, // 时间
//   //           'text': { 'type': 'text', 'analyzer': 'ik_max_word', 'search_analyzer': 'ik_smart' }, // 客户的memory
//   //         }
//   //       }
//   //   )
//   // }, 30000)
//
import ElasticSearchService from '../../../../model/elastic_search/elastic_search'

it('创建 chatID的 memory, 正统命名', async () => {
  // await ElasticSearchService.deleteIndex('user_memory_test')

  await ElasticSearchService.createIndex('moer_user_memory', {
    'properties': {
      'embeddings': { 'type': 'dense_vector', index: true },
      'metadata.chat_id': { 'type': 'keyword', index: true }, // 客户的 chat_id
      'metadata.summary': { 'type': 'text', 'analyzer': 'ik_max_word', 'search_analyzer': 'ik_smart' }, // 客户的memory
      'metadata.timestamp': { 'type': 'date', index: true }, // 时间
      'text': { 'type': 'text', 'analyzer': 'ik_max_word', 'search_analyzer': 'ik_smart' }, // 客户的memory
    }
  })
}, 30000)

it('插入测试', async () => {
  await ElasticSearchService.insertDocument('user_memory_test', {
    'metadata.chat_id': 'mtfker',
    'metadata.summary': 'u',
    'metadata.timestamp': new Date(),
    'text': 'hi'
  })
}, 60000)

it('查询', async () => {
  const res = await ElasticSearchService.search('user_memory',
    {
      'bool': {
        'should':[
          {
            'term': {
              'metadata.chat_id': '7881301847025991_1688856322643146'
            }
          }]
      }
    }, 1, {
      sort: {
        'metadata.timestamp': {
          order: 'desc'
        }
      }
    })

  console.log(res[0]._source.text as string)
}, 60000)
//
//   it('插入数据到 user_memory', async () => {
//     const data = JSON.parse(await FileHelper.readFile(path.join(__dirname, 'course_38.json')))
//     // Embedding 插入
//     // 构建 embedding, 插入到 es 中
//     const clientArgs: ElasticClientArgs = {
//       client: new Client(clientConfig),
//       indexName: 'user_memory',
//     }
//     // Index documents
//     const docs = data.map((item) => {
//       return new Document({
//         metadata: item,
//         pageContent: item.summary,
//       })
//     })
//
//     console.log(docs.length)
//
//     const batchSize = 100
//     const batches = Math.ceil(docs.length / batchSize)
//     for (let i = 0; i < batches; i++) {
//       const batch = docs.slice(i * batchSize, (i + 1) * batchSize)
//       const embeddings = AzureOpenAIEmbedding.getInstance()
//       const vectorStore = new ElasticVectorSearch(embeddings, clientArgs)
//       await vectorStore.addDocuments (batch)
//       console.log(`Indexed ${100 * (i + 1)} documents`)
//     }
//
//   }, 9E8)
//
//   it('插入客户 memory, user_memory_index', async () => {
//     // 读取 userMemory.json 文件
//     const data = JSON.parse(await FileHelper.readFile(path.join(__dirname, 'userMemory.json')))
//
//     // 转换数据格式
//     const qaData: QA[] = data.map((item) => ({
//       q: item.summary,
//       a: item.chat_id,
//       doc: item.timestamp
//     }))
//
//     const clientArgs: ElasticClientArgs = {
//       client: new Client(clientConfig),
//       indexName: 'user_memory_index',
//     }
//
//     // 构建文档
//     const docs = qaData.map((item) => {
//       return new Document({
//         metadata: item,
//         pageContent: item.q,
//       })
//     })
//
//     console.log(`Total documents: ${docs.length}`)
//
//     // 批量插入文档
//     const batchSize = 100
//     const batches = Math.ceil(docs.length / batchSize)
//     for (let i = 0; i < batches; i++) {
//       const batch = docs.slice(i * batchSize, (i + 1) * batchSize)
//       const embeddings = AzureOpenAIEmbedding.getInstance()
//       const vectorStore = new ElasticVectorSearch(embeddings, clientArgs)
//       await vectorStore.addDocuments(batch)
//       console.log(`Indexed ${Math.min((i + 1) * batchSize, docs.length)} documents`)
//     }
//
//     console.log('All documents have been indexed successfully.')
//
//   }, 30000)
//
//   it('删除 user_memory_index 部分数据', async () => {
//     const client = new Client(clientConfig)
//     const indexName = 'user_memory_index'
//     try {
//       const response = await client.deleteByQuery({
//         index: indexName,
//         body: {
//           query: {
//             term: {
//               'metadata.a': '7881300846030208_1688854546332791'
//             }
//           }
//         }
//       })
//
//       console.log(`Deleted ${response.deleted} documents`)
//     } catch (error) {
//       console.error('Error deleting documents:', error)
//     } finally {
//       await client.close()
//     }
//   }, 30000)
//
// })