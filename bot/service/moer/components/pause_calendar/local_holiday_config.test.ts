import { LocalHolidayConfig } from './local_holiday_config'
import { DataService } from '../../getter/getData'
import dayjs from 'dayjs'

describe('LocalHolidayConfig 本地假期配置测试', () => {
  describe('基础功能测试', () => {
    it('应该正确识别假期日期', () => {
      // 测试现有配置的假期：2025-04-21 到 2025-05-03
      const holidayDate = new Date('2025-04-25')
      const isHoliday = LocalHolidayConfig.isDateInHoliday(holidayDate)
      expect(isHoliday).toBe(true)
    })

    it('应该正确识别非假期日期', () => {
      const workDate = new Date('2025-03-15')
      const isHoliday = LocalHolidayConfig.isDateInHoliday(workDate)
      expect(isHoliday).toBe(false)
    })

    it('应该正确处理假期边界日期', () => {
      // 测试假期开始日期
      const startDate = new Date('2025-04-21')
      expect(LocalHolidayConfig.isDateInHoliday(startDate)).toBe(true)

      // 测试假期结束日期
      const endDate = new Date('2025-05-03')
      expect(LocalHolidayConfig.isDateInHoliday(endDate)).toBe(true)

      // 测试假期前一天
      const beforeStart = new Date('2025-04-20')
      expect(LocalHolidayConfig.isDateInHoliday(beforeStart)).toBe(false)

      // 测试假期后一天
      const afterEnd = new Date('2025-05-04')
      expect(LocalHolidayConfig.isDateInHoliday(afterEnd)).toBe(false)
    })
  })

  describe('期数计算逻辑测试', () => {
    it('应该正确计算原始期数（按周计算）', () => {
      // 测试基准日期后的某个日期
      const testDate = new Date('2025-01-06') // 2024-01-29 后的某个周一
      const period = LocalHolidayConfig.calculatePeriod(testDate)

      // 验证期数计算逻辑（具体数值取决于基准日期）
      expect(typeof period).toBe('number')
      expect(period).toBeGreaterThanOrEqual(0)
    })

    it('应该正确处理假期对期数的影响', () => {
      // 使用现有假期配置：2025-04-21 到 2025-05-03
      const beforeHoliday = new Date('2025-04-14') // 假期前
      const duringHoliday = new Date('2025-04-25') // 假期中
      const afterHoliday = new Date('2025-05-06')  // 假期后

      const periodBefore = LocalHolidayConfig.calculatePeriod(beforeHoliday)
      const periodDuring = LocalHolidayConfig.calculatePeriod(duringHoliday)
      const periodAfter = LocalHolidayConfig.calculatePeriod(afterHoliday)

      // 假期期间的期数应该受到影响（被暂停）
      expect(typeof periodBefore).toBe('number')
      expect(typeof periodDuring).toBe('number')
      expect(typeof periodAfter).toBe('number')

      // 假期后的期数应该体现顺延效果
      expect(periodAfter).toBeGreaterThanOrEqual(periodBefore)
    })
  })

  describe('期数计算测试', () => {
    it('应该正确计算期数', () => {
      // 基准日期是2024-01-29，第0期
      const testDate = new Date('2025-01-06')
      const period = LocalHolidayConfig.calculatePeriod(testDate)

      // 验证期数计算逻辑
      expect(period).toBeGreaterThanOrEqual(0)
      expect(typeof period).toBe('number')
    })

    it('应该正确获取当前期数和下一期期数', () => {
      const currentPeriod = LocalHolidayConfig.getCurrentPeriod()
      const nextPeriod = LocalHolidayConfig.getNextPeriod()

      expect(typeof currentPeriod).toBe('number')
      expect(typeof nextPeriod).toBe('number')
      expect(nextPeriod).toBe(currentPeriod + 1)
    })
  })

  describe('假期信息获取测试', () => {
    it('应该能够获取假期信息', () => {
      const allHolidays = LocalHolidayConfig.getAllHolidays()
      expect(Array.isArray(allHolidays)).toBe(true)
      expect(allHolidays.length).toBeGreaterThan(0)

      // 检查假期格式
      const firstHoliday = allHolidays[0]
      expect(firstHoliday).toHaveProperty('startDate')
      expect(firstHoliday).toHaveProperty('endDate')
      expect(typeof firstHoliday.startDate).toBe('string')
      expect(typeof firstHoliday.endDate).toBe('string')
    })

    it('应该能够获取基准配置信息', () => {
      const anchorInfo = LocalHolidayConfig.getAnchorInfo()
      expect(anchorInfo).toHaveProperty('anchorDate')
      expect(anchorInfo).toHaveProperty('anchorPeriod')
      expect(anchorInfo).toHaveProperty('calculationMethod')
      expect(anchorInfo.anchorDate).toBe('2024-01-29')
      expect(anchorInfo.anchorPeriod).toBe(0)
      expect(anchorInfo.calculationMethod).toBe('weekly')
    })
  })

  describe('期数预览功能测试', () => {
    it('应该能够预览期数计算结果', () => {
      const preview = LocalHolidayConfig.previewPeriods(
        new Date('2025-04-20'),
        new Date('2025-04-25')
      )

      expect(Array.isArray(preview)).toBe(true)
      expect(preview.length).toBe(6) // 6天的预览

      // 检查预览格式
      const firstDay = preview[0]
      expect(firstDay).toHaveProperty('date')
      expect(firstDay).toHaveProperty('period')
      expect(firstDay).toHaveProperty('isHoliday')

      // 4月20日不是假期，4月21日开始是假期
      expect(preview[0].isHoliday).toBe(false) // 4月20日
      expect(preview[1].isHoliday).toBe(true)  // 4月21日
    })
  })

  describe('DataService 集成测试', () => {
    it('DataService 应该能够检查假期状态', () => {
      // 测试假期日期
      const holidayDate = new Date('2025-04-25')
      const isInHoliday = DataService.isDateInHoliday(holidayDate)
      expect(isInHoliday).toBe(true)

      // 测试非假期日期
      const workDate = new Date('2025-03-15')
      const isNotInHoliday = DataService.isDateInHoliday(workDate)
      expect(isNotInHoliday).toBe(false)
    })

    it('DataService 应该能够获取假期信息', () => {
      // 直接测试假期日期
      const holidayDate = new Date('2025-04-25')
      const isInHoliday = DataService.isDateInHoliday(holidayDate)
      expect(isInHoliday).toBe(true)

      // 测试当前不在假期的情况
      const isCurrentlyInHoliday = DataService.isCurrentlyInHoliday()
      const holidayInfo = DataService.getCurrentHolidayInfo()

      // 当前日期不在假期内，所以应该返回 false 和 null
      expect(typeof isCurrentlyInHoliday).toBe('boolean')
      expect(holidayInfo === null || typeof holidayInfo === 'object').toBe(true)
    })

    it('DataService 期数计算应该使用新的假期配置', () => {
      const currentPeriod = DataService.getCurrentWeekCourseNo()
      const nextPeriod = DataService.getNextWeekCourseNo()

      expect(typeof currentPeriod).toBe('number')
      expect(typeof nextPeriod).toBe('number')
      expect(nextPeriod).toBe(currentPeriod + 1)
    })
  })

  describe('边界情况测试', () => {
    it('应该正确处理查询日期早于基准日期的情况', () => {
      const earlyDate = new Date('2023-01-01')
      const period = LocalHolidayConfig.calculatePeriod(earlyDate)
      expect(period).toBe(0)
    })

    it('应该正确处理无效日期输入', () => {
      const invalidDate = new Date('invalid')
      expect(() => {
        LocalHolidayConfig.isDateInHoliday(invalidDate)
      }).not.toThrow()
    })

    it('应该正确处理查询日期早于基准日期的情况', () => {
      const earlyDate = new Date('2023-01-01')
      const period = LocalHolidayConfig.calculatePeriod(earlyDate)
      expect(period).toBe(0)
    })
  })
})

describe('使用示例', () => {
  it('展示如何在业务逻辑中使用假期检查', () => {
    console.log('=== 假期检查使用示例 ===')

    // 检查当前是否在假期内
    const isInHoliday = DataService.isCurrentlyInHoliday()
    console.log('当前是否在假期内:', isInHoliday)

    if (isInHoliday) {
      const holidayInfo = DataService.getCurrentHolidayInfo()
      console.log('假期信息:', holidayInfo)
    }

    // 检查特定日期
    const testDate = new Date('2025-05-01')
    const isTestDateHoliday = DataService.isDateInHoliday(testDate)
    console.log('2025年5月1日是否是假期:', isTestDateHoliday)

    // 获取期数
    const currentPeriod = DataService.getCurrentWeekCourseNo()
    const nextPeriod = DataService.getNextWeekCourseNo()
    console.log('当前期数:', currentPeriod, '下一期:', nextPeriod)
  })


  it('获取指定日期的期数', async () => {
    console.log(LocalHolidayConfig.getCurrentPeriod(new Date('2025-10-01')))
    console.log(LocalHolidayConfig.getCurrentPeriod(new Date('2025-10-06')))
    console.log(LocalHolidayConfig.getCurrentPeriod(new Date('2025-09-29')))
    console.log(LocalHolidayConfig.getCurrentPeriod(new Date('2025-10-12')))
    console.log(LocalHolidayConfig.getCurrentPeriod(new Date('2025-10-13')))
  }, 30000)
})
