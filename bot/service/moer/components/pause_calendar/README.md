# 本地假期配置系统

## 概述

这是一个轻量级的假期管理系统，使用本地配置文件来管理课程期数计算中的假期暂停。相比数据库方案，这个方案改动最小，无需异步操作，便于快速部署和使用。

## 核心特性

- ✅ **零数据库依赖**：使用本地配置，无需数据库操作
- ✅ **同步操作**：所有方法都是同步的，无需改动现有调用方式
- ✅ **最小改动**：现有的 `getCurrentWeekCourseNo()` 和 `getNextWeekCourseNo()` 方法保持不变
- ✅ **按周计算**：期数按照 ISO 周计算，每次上课必定是周一，符合实际业务逻辑
- ✅ **自动计算**：添加假期后，期数自动正确计算，实现顺延效果
- ✅ **容错机制**：提供回退方案，确保系统稳定性

## 快速开始

### 1. 添加假期

编辑 `local_holiday_config.ts` 文件中的 `HOLIDAY_PERIODS` 数组：

```typescript
private static readonly HOLIDAY_PERIODS: HolidayPeriod[] = [
  // 现有假期
  {
    startDate: '2025-04-21',
    endDate: '2025-05-03',
    reason: '原硬编码假期'
  },
  
  // 添加新假期 - 五一假期
  {
    startDate: '2025-05-01',
    endDate: '2025-05-03',
    reason: '五一劳动节假期'
  },
  
  // 添加新假期 - 国庆假期
  {
    startDate: '2025-10-01',
    endDate: '2025-10-07',
    reason: '国庆节假期'
  }
]
```

### 2. 在业务逻辑中检查假期

```typescript
import { DataService } from '../../getter/getData'

// 检查当前是否在假期内
const isInHoliday = DataService.isCurrentlyInHoliday()
if (isInHoliday) {
  console.log('假期期间，暂停执行某些任务')
  return
}

// 获取假期详情
const holidayInfo = DataService.getCurrentHolidayInfo()
if (holidayInfo) {
  console.log(`当前假期：${holidayInfo.reason}`)
  console.log(`假期时间：${holidayInfo.startDate} 到 ${holidayInfo.endDate}`)
}

// 检查指定日期是否在假期内
const isSpecificDateHoliday = DataService.isDateInHoliday(new Date('2025-05-01'))
```

### 3. 获取期数（无需改动现有代码）

```typescript
// 现有代码无需修改，自动使用新的假期计算逻辑
const currentPeriod = DataService.getCurrentWeekCourseNo()
const nextPeriod = DataService.getNextWeekCourseNo()
```

## API 参考

### DataService 新增方法

#### `isCurrentlyInHoliday(): boolean`
检查当前是否在假期内。

#### `getCurrentHolidayInfo(): HolidayInfo | null`
获取当前假期信息，如果不在假期内则返回 null。

#### `isDateInHoliday(date: Date): boolean`
检查指定日期是否在假期内。

### LocalHolidayConfig 核心方法

#### `getCurrentPeriod(): number`
获取当前期数。

#### `getNextPeriod(): number`
获取下一期期数。

#### `calculatePeriod(date: Date): number`
计算指定日期的期数。

#### `previewPeriods(startDate: Date, endDate: Date)`
预览指定时间段的期数计算结果（用于调试）。

## 使用场景示例

### 场景1：SOP消息发送前检查假期

```typescript
async function sendSOPMessage(chatId: string) {
  // 假期期间暂停发送SOP消息
  if (DataService.isCurrentlyInHoliday()) {
    logger.log('假期期间，跳过SOP消息发送', { chatId })
    return
  }
  
  // 正常发送逻辑
  await sendMessage(chatId, sopContent)
}
```

### 场景2：任务调度时考虑假期

```typescript
function scheduleTask(chatId: string, delayDays: number) {
  const targetDate = dayjs().add(delayDays, 'day').toDate()
  
  if (DataService.isDateInHoliday(targetDate)) {
    logger.log('目标日期是假期，延后执行任务', { 
      chatId, 
      originalDate: dayjs(targetDate).format('YYYY-MM-DD') 
    })
    // 可以选择延后到假期结束后执行
    return
  }
  
  // 正常调度任务
  scheduleTaskAt(chatId, targetDate)
}
```

### 场景3：期数显示和计算

```typescript
function getCourseInfo(chatId: string) {
  const currentPeriod = DataService.getCurrentWeekCourseNo()
  const nextPeriod = DataService.getNextWeekCourseNo()
  
  let message = `当前是第 ${currentPeriod} 期，下一期是第 ${nextPeriod} 期`
  
  if (DataService.isCurrentlyInHoliday()) {
    const holidayInfo = DataService.getCurrentHolidayInfo()
    message += `\n当前在${holidayInfo?.reason}期间，期数暂停计算`
  }
  
  return message
}
```

## 调试和预览

### 预览期数计算结果

```typescript
import { LocalHolidayConfig } from './local_holiday_config'

// 预览一周的期数计算
const preview = LocalHolidayConfig.previewPeriods(
  new Date('2025-04-20'),
  new Date('2025-04-26')
)

console.log('期数预览:')
preview.forEach(day => {
  console.log(`${day.date}: 第${day.period}期 ${day.isHoliday ? '(假期)' : ''}`)
})
```

### 查看配置信息

```typescript
// 查看所有假期配置
const holidays = LocalHolidayConfig.getAllHolidays()
console.log('所有假期:', holidays)

// 查看基准配置
const anchorInfo = LocalHolidayConfig.getAnchorInfo()
console.log('基准配置:', anchorInfo)
```

## 测试

运行测试用例：

```bash
npm test -- local_holiday_config.test.ts
```

## 扩展性

如果将来需要更复杂的假期管理功能，可以：

1. 添加假期类型分类（法定节假日、公司假期等）
2. 支持按年度重复的假期配置
3. 添加假期审批流程
4. 当前的 API 接口设计具有良好的扩展性，无需修改业务代码

## 计算逻辑说明

### 期数计算方式

系统使用**按周计算**的方式，而不是简单的天数整除：

1. **基准设置**：2024-01-29 为第 0 期开始日期（开营日）
2. **周计算**：使用 ISO 周标准，每周从周一开始
3. **期数公式**：`期数 = 原始周数差 - 暂停周数`
4. **假期处理**：假期跨越的周数会从总期数中扣除

### 示例说明

- 从周日到周一：跨越一周，期数 +1
- 假期跨越 2 周：期数计算时减去 2 周
- 每次上课必定是周一，符合实际业务逻辑

## 注意事项

1. **日期格式**：假期配置中的日期必须使用 'YYYY-MM-DD' 格式
2. **时区**：所有日期计算基于系统本地时区
3. **边界包含**：假期的开始和结束日期都包含在内
4. **周计算**：期数按 ISO 周计算，确保与实际上课周期一致
5. **容错机制**：如果计算出错，会自动回退到原始的硬编码逻辑

## 配置文件位置

假期配置位于：`bot/service/moer/components/pause_calendar/local_holiday_config.ts`

修改配置后无需重启服务，但建议在低峰期进行修改以确保一致性。
