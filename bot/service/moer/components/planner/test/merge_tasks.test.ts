import { TaskManager } from '../task/task_manager'
import { UUID } from '../../../../../lib/uuid/uuid'

describe('Test', function () {
  beforeAll(() => {

  })

  it('should pass', async () => {
    const chat_id = '7881301068910530_1688857003605938'
    const toAddTasks = []
    const round_id = UUID.v4()

    const tasks =  await TaskManager.getFlexibleActiveTasks(chat_id)

    if (toAddTasks.length + tasks.length > 5) {
      const filteredTasks = await TaskManager.mergeTasks(chat_id, toAddTasks, tasks, round_id)
    }
  })
})