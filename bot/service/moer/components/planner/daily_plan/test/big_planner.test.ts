import { PromptTemplate } from '@langchain/core/prompts'
import { LLM } from '../../../../../../lib/ai/llm/LLM'
import { getPrompt } from '../../../agent/prompt'
import { <PERSON>Planner } from '../big_planner'
import { DataService } from '../../../../getter/getData'
import { ContextBuilder } from '../../../agent/context'
import { taskTimeToCourseTime, IScheduleTime } from '../../../schedule/creat_schedule_task'
import dayjs from 'dayjs'
import { ISchedulePlannerTask, Planner } from '../../plan/planner'
import { ChatStatStoreManager } from '../../../../storage/chat_state_store'
import ElasticSearchService from '../../../../../../model/elastic_search/elastic_search'
import { MemoryRecall } from '../../../memory/memory_search'
import { ChatHistoryService } from '../../../chat_history/chat_history'
import MockDate from 'mockdate'
import { UserSlots } from '../../../flow/helper/slotsExtract'
import { PlanOperationsSimulator, TaskItem } from './plan_operations_simulator'
import { Config } from '../../../../../../config/config'
import { TaskManager } from '../../task/task_manager'
import { Queue } from 'bullmq'
import { getBotId } from '../../../../../../config/chat_id'
import { RedisDB } from '../../../../../../model/redis/redis'


/**
 * BigPlanner Mock 测试套件
 *
 * 这个测试套件用于模拟 BigPlanner 在不同时间点的规划生成，主要功能：
 * 1. Mock getCurrentTime 函数，模拟不同日期的时间
 * 2. Mock context 数据（客户画像、记忆、行为等）
 * 3. Mock 每天的 SOP 任务数据
 * 4. 循环测试从上课前两天到上课周结束的每一天
 *
 * 使用方法：
 * 1. 修改 prepareDailySOPMap() 函数，填入每天的 SOP 数据
 * 2. 修改 prepareCustomerPortrait()、prepareMemories() 等函数，填入客户数据
 * 3. 运行测试，查看每天生成的计划结果
 *
 * 注意：这是一个测试架子，客户需要根据实际需求填充具体的测试数据
 */
describe('BigPlanner Mock Tests', function () {
  let getCurrentTimeSpy: jest.SpyInstance
  let filterSOPByDateSpy: jest.SpyInstance
  let getCustomerPortraitSpy: jest.SpyInstance
  let getRecentMemoriesSpy: jest.SpyInstance
  let getCustomerBehaviorSpy: jest.SpyInstance

  beforeEach(() => {
    // 设置所有需要的 spy
    getCurrentTimeSpy = jest.spyOn(DataService, 'getCurrentTime')
    filterSOPByDateSpy = jest.spyOn(Planner, 'filterTasksByDate')
    getCustomerPortraitSpy = jest.spyOn(ContextBuilder, 'getCustomerPortrait')
    getRecentMemoriesSpy = jest.spyOn(ContextBuilder, 'getRecentMemories')
    getCustomerBehaviorSpy = jest.spyOn(ContextBuilder, 'getCustomerBehavior')
  })

  afterEach(() => {
    // 清理所有 spy
    getCurrentTimeSpy.mockRestore()
    filterSOPByDateSpy.mockRestore()
    getCustomerPortraitSpy.mockRestore()
    getRecentMemoriesSpy.mockRestore()
    getCustomerBehaviorSpy.mockRestore()
  })

  /**
     * 准备每天的 SOP 数据 map
     * 客户可以根据实际需求修改这个函数
     */
  function prepareDailySOPMap(): Record<string, any[]> {
    return {
      '-2':  [
        {
          'time': '2025-08-29 20:45:00',
          'description': '周五统一拉群'
        }
      ],
      '-1': [
        {
          'time': '2025-08-30 08:30:00',
          'description': '【7】周六催先导课'
        },
        {
          'time': '2025-08-30 19:03:00',
          'description': '介绍老师的介绍'
        }
      ],
      '0': [
        {
          'time': '2025-08-31 19:00:00',
          'description': '提醒晚上的开营仪式'
        },
        {
          'time': '2025-08-31 20:30:00',
          'description': 'DAY1前拉注意力，发送合集'
        }
      ],
      '1': [
        {
          'time': '2025-09-01 08:00:00',
          'description': 'day1早上通知第一课（分为进群和未进群人群）【所有人】'
        },
        {
          'time': '2025-09-01 11:59:59',
          'description': 'DAY1 上课前催能量测评【prompt】'
        },
        {
          'time': '2025-09-01 15:30:00',
          'description': 'Day1再次挖需（针对未回复挖需人群）'
        },
        {
          'time': '2025-09-01 18:30:00',
          'description': 'day1确认所有人有上课权限'
        },
        {
          'time': '2025-09-01 19:55:00',
          'description': ' DAY1  晚上8点的通知'
        },
        {
          'time': '2025-09-01 20:00:38',
          'description': 'day1八点开课'
        },
        {
          'time': '2025-09-01 20:05:19',
          'description': 'day1开课五分钟催到课'
        },
        {
          'time': '2025-09-01 20:30:01',
          'description': 'DAY1第一节课催到课'
        },
        {
          'time': '2025-09-01 21:00:00',
          'description': 'day1催第一节课到课第二次'
        },
        {
          'time': '2025-09-01 21:43:00',
          'description': 'day1 【动态sop】上完课回放+未完课提醒+未上课提醒'
        }
      ],
      '2': [
        {
          'time': '2025-09-02 07:30:00',
          'description': 'day2 早上课程预告（完课+未完课）（催回放第一次）'
        },
        {
          'time': '2025-09-02 11:00:33',
          'description': 'DAY2没做任何动作客户沟通'
        },
        {
          'time': '2025-09-02 11:59:59',
          'description': 'day2中午提醒：完课礼发送&提醒看完回放（催回放第2次）'
        },
        {
          'time': '2025-09-02 15:40:00',
          'description': 'day2下午第一节课未完课对话过（催回放第3次）'
        },
        {
          'time': '2025-09-02 17:03:00',
          'description': 'DAY2下午催到课回放（第4次）未回复过客户'
        },
        {
          'time': '2025-09-02 19:55:00',
          'description': 'day2 8点上课通知'
        },
        {
          'time': '2025-09-02 20:10:00',
          'description': 'day2课中10分钟'
        },
        {
          'time': '2025-09-02 20:30:24',
          'description': 'DAY2上课期间八点半催课'
        },
        {
          'time': '2025-09-02 21:15:00',
          'description': 'day2 完课人群作业财富果园画面'
        },
        {
          'time': '2025-09-02 21:20:00',
          'description': 'day2 完课人群问感受+到课提醒回放+未到课问原因提兴趣'
        },
        {
          'time': '2025-09-02 21:30:00',
          'description': 'day2 未完课人群+第一节完课（有互动）人群发回放+再次提醒财富果园'
        }
      ],
      '3': [
        {
          'time': '2025-09-03 07:30:00',
          'description': 'day3早安完课招呼预告第三节课+催财富果园+催回放+针对未看人群（第一次催回放）'
        },
        {
          'time': '2025-09-03 12:30:59',
          'description': 'day3 中午消息：催财富果园+催看完回放+预告第三节课（第2次催回放）'
        },
        {
          'time': '2025-09-03 14:30:50',
          'description': 'day3催促有财富问题的人看回放'
        },
        {
          'time': '2025-09-03 15:30:00',
          'description': 'day 3 最后一次催财富果园画面+激活低意向人群'
        },
        {
          'time': '2025-09-03 17:21:00',
          'description': 'day3  五点半预告红靴子课程&提醒没有回放'
        },
        {
          'time': '2025-09-03 18:35:32',
          'description': 'DAY3六点半语音提醒看完三节课有完课礼'
        },
        {
          'time': '2025-09-03 19:02:00',
          'description': 'day3 7点上课通知'
        },
        {
          'time': '2025-09-03 19:55:00',
          'description': 'day3 8点上课通知'
        },
        {
          'time': '2025-09-03 20:00:00',
          'description': 'day3开课时的催课通知'
        },
        {
          'time': '2025-09-03 20:05:01',
          'description': 'day3开课五分钟催到课'
        },
        {
          'time': '2025-09-03 20:30:17',
          'description': 'day3课程中催课'
        },
        {
          'time': '2025-09-03 21:10:46',
          'description': 'day3课中第三次催课'
        },
        {
          'time': '2025-09-03 22:00:00',
          'description': 'day3课中最后一次催课'
        },
        {
          'time': '2025-09-03 22:25:00',
          'description': 'Day3针对未到课人群的加播预告'
        },
        {
          'time': '2025-09-03 22:30:00',
          'description': 'Day3：下单人群预告明天课程'
        },
        {
          'time': '2025-09-03 22:30:00',
          'description': 'day3 未下单人群问感受&催回放'
        },
        {
          'time': '2025-09-03 22:40:00',
          'description': 'day3 最后一条消息：第三节课到课未下单（冥想作用+预告）'
        }
      ],
      '4': [
        {
          'time': '2025-09-04 07:32:00',
          'description': 'Day4早上好，预告+催回放'
        },
        {
          'time': '2025-09-04 09:43:00',
          'description': 'Day4调研加播课(已下单&高意向人群）'
        },
        {
          'time': '2025-09-04 12:27:00',
          'description': 'day4完课礼（第三节课到课）&高意向客户回顾'
        },
        {
          'time': '2025-09-04 14:00:00',
          'description': 'Day4通知晚上加播课'
        },
        {
          'time': '2025-09-04 15:00:00',
          'description': 'Day4：15:00提醒回放'
        },
        {
          'time': '2025-09-04 18:01:08',
          'description': 'day4下午六点发第四节课预告'
        },
        {
          'time': '2025-09-04 19:20:10',
          'description': 'day4七点分享往期案例（有视频）'
        },
        {
          'time': '2025-09-04 19:55:00',
          'description': 'day4课前十分钟提醒'
        },
        {
          'time': '2025-09-04 20:01:17',
          'description': 'day4八点上课提醒'
        },
        {
          'time': '2025-09-04 20:10:50',
          'description': 'day4课中催到课第一次'
        },
        {
          'time': '2025-09-04 20:40:07',
          'description': 'day4课中催到课第二次'
        },
        {
          'time': '2025-09-04 21:15:16',
          'description': 'day4课中催到课（第三次最后一次催）'
        },
        {
          'time': '2025-09-04 22:26:00',
          'description': 'Day4课后回访【prompt】'
        },
        {
          'time': '2025-09-04 22:30:00',
          'description': 'day4课结束后发下单链接（三四节课都到课）'
        },
        {
          'time': '2025-09-04 22:35:51',
          'description': 'day4第四节课到课给未看完人群'
        }
      ],
      '5': [
        {
          'time': '2025-09-05 08:08:00',
          'description': 'DAY5未下单打招呼整理回放链接合集'
        },
        {
          'time': '2025-09-05 11:20:48',
          'description': 'day5上午提醒看回放'
        },
        {
          'time': '2025-09-05 12:30:00',
          'description': 'DAY5中午恭喜结营'
        },
        {
          'time': '2025-09-05 14:20:26',
          'description': 'day5文字总结系统课精华'
        },
        {
          'time': '2025-09-05 16:30:55',
          'description': 'day5下午提醒第三&第四节课补课人群'
        },
        {
          'time': '2025-09-05 17:45:32',
          'description': 'day5下午问三四节课看完人没下单原因'
        },
        {
          'time': '2025-09-05 18:30:48',
          'description': 'day5晚上结营话术'
        },
        {
          'time': '2025-09-05 19:30:56',
          'description': 'day5最后一次问下单（用邮寄垫子逼单）'
        },
        {
          'time': '2025-09-05 20:20:40',
          'description': 'day5晚上分手信【prompt】-子豪版本'
        }
      ],
      '6': [
        {
          'time': '2025-09-06 09:33:47',
          'description': 'DAY6上午打招呼发总结'
        },
        {
          'time': '2025-09-06 12:19:44',
          'description': 'day6提醒看3&4节课回放'
        },
        {
          'time': '2025-09-06 15:41:07',
          'description': 'day6下午询问完课礼是否都发送'
        },
        {
          'time': '2025-09-06 17:30:41',
          'description': 'day6问询时间问题延期'
        }
      ],
      '7': [
        {
          'time': '2025-09-07 12:30:31',
          'description': 'day7学员案例分享'
        },
        {
          'time': '2025-09-07 16:00:54',
          'description': 'day7效果不满意给空间'
        },
        {
          'time': '2025-09-07 18:08:23',
          'description': 'day7周日最后一条消息用优惠名额逼单'
        }
      ]
    }
  }

  /**
     * 准备固定的客户画像数据
     * 客户可以根据实际需求修改这个函数
     *
     * 新增功能：支持根据日期获取客户画像
     * @param chat_id 聊天ID
     * @param targetDate 可选的目标日期，格式：YYYY-MM-DD。如果提供，将获取该日期的第一条消息的客户画像
     */
  async function prepareCustomerPortrait(chat_id: string, targetDate?: string): Promise<string> {
    if (targetDate) {
      // 使用新的按日期获取客户画像的功能
      return await getCustomerPortraitByDate(chat_id, targetDate)
    }

    // 默认行为：获取当前的客户画像
    return await ContextBuilder.getCustomerPortrait(chat_id)
  }

  async function getCustomerPortraitByDate(chatId: string, targetDate: string): Promise<string> {
    const userSlots =  await ChatHistoryService.getCustomerPortraitByDate(chatId, targetDate)
    const tmp = UserSlots.fromRecord(userSlots)

    return tmp.toString()
  }

  /**
     * 准备固定的记忆数据
     * 客户可以根据实际需求修改这个函数
     */
  async function prepareMemories(chat_id: string, currentDate: dayjs.Dayjs): Promise<string[]> {
    const elasticSearchResult = await ElasticSearchService.search(
      MemoryRecall.indexName,
      {
        bool: {
          must: [
            {
              term: { 'metadata.chat_id': chat_id }
            },
            {
              range: {
                'metadata.timestamp': {
                  lt: currentDate.toISOString()  // 只取 currentDate 之前的数据
                }
              }
            }
          ]
        }
      },
      5,
      {
        sort: {
          'metadata.timestamp': {
            order: 'desc'
          }
        }
      }
    )

    return elasticSearchResult.map((item) => item._source.text as string)
  }

  /**
     * 准备客户行为数据，根据当前测试日期动态生成
     * 客户可以根据实际需求修改这个函数
     */
  function prepareCustomerBehavior(day: number): string {
    if (day <= 1) {
      return '- 小讲堂海浪冥想（完成）'
    } else if (day === 2) {
      return `- 小课堂海浪冥想（完成）
- 第一课情绪减压（已完成）`
    } else if (day === 3) {
      return `- 小课堂海浪冥想（完成）
- 第一课情绪减压（已完成）
- 第二课财富唤醒（已完成）`
    } else if (day === 4) {
      return `- 小课堂海浪冥想（完成）
- 第一课情绪减压（已完成）
- 第二课财富唤醒（已完成）
- 第三课红鞋子飞跃（已完成）`
    } else {
      return `- 小课堂海浪冥想（完成）
- 第一课情绪减压（已完成）
- 第二课财富唤醒（已完成）
- 第三课红鞋子飞跃（已完成）
- 第四课蓝鹰预演（已完成）`
    }
  }

  it('获取 memory 测试', async () => {
    console.log(await prepareMemories('7881303394909101_1688857003605938', dayjs('2025-08-19')))
  }, 30000)

  it('should generate plans for each day from pre-course to course end', async () => {
    const chat_id = '7881303189944040_1688857949631398' // 测试用的 chat_id

    try {
      await ChatStatStoreManager.initState(chat_id)

      // 1. 获取课程开始时间
      const courseStartTime = await DataService.getCourseStartTime(chat_id)

      // 2. 计算时间范围：上课前两天到上课周结束（周日）
      const startDate = dayjs(courseStartTime).subtract(2, 'day')
      const endDate = dayjs(courseStartTime).add(6, 'day') // 周日

      console.log('测试时间范围:', startDate.format('YYYY-MM-DD'), '到', endDate.format('YYYY-MM-DD'))

      // 3. 准备每天的 SOP 数据 map
      const dailySOPMap = prepareDailySOPMap()

      // 5. 循环每一天进行测试
      let currentDate = startDate
      const results: Array<{ date: string, result: any }> = []

      while (currentDate.isBefore(endDate) || currentDate.isSame(endDate, 'day')) {
        MockDate.set(currentDate.toDate())

        const dayKey = currentDate.format('YYYY-MM-DD')
        const dayName = currentDate.format('dddd')

        console.log(`\n=== 测试日期: ${dayKey} (${dayName}) ===`)

        const memories = await prepareMemories(chat_id, currentDate)
        const customerPortrait = await prepareCustomerPortrait(chat_id, currentDate.format())

        getCustomerPortraitSpy.mockResolvedValue(customerPortrait)
        getRecentMemoriesSpy.mockResolvedValue(memories)

        try {
          // Mock getCurrentTime 返回当天 0点0分对应的课程时间
          const mockTime = await taskTimeToCourseTime(
            currentDate.startOf('day').toDate(),
            chat_id
          )
          getCurrentTimeSpy.mockResolvedValue(mockTime)
          console.log('Mock 时间信息:', JSON.stringify(mockTime, null, 2))

          const day = mockTime.is_course_week ? mockTime.day : mockTime.day + -8

          // Mock 当天的客户行为数据
          const customerBehavior = prepareCustomerBehavior(day)
          getCustomerBehaviorSpy.mockResolvedValue(customerBehavior)

          // Mock 当天的 SOP 数据
          const dailySOP = dailySOPMap[day] || []
          filterSOPByDateSpy.mockResolvedValue(dailySOP)
          console.log('当天 SOP 任务:', JSON.stringify(dailySOP, null, 2))

          // 调用 generatePlan
          const result = await BigPlanner.generatePlan(chat_id)

          // 记录结果
          results.push({ date: dayKey, result })

          // 输出结果摘要
          if (result) {
            console.log('生成计划成功')
            console.log('思考过程:', `${result.planResponse.think  }`)
            console.log('计划操作:', JSON.stringify(result.planResponse.plans, null, 4))
            const res = PlanOperationsSimulator.simulateOperations(result.context.existing_task, result.planResponse.plans)

            // // 输出格式化结果
            // const formattedResult = PlanOperationsSimulator.formatComparisonResult(res)
            // console.log(formattedResult)
          } else {
            console.log('生成计划失败')
          }

        } catch (error: any) {
          console.error(`日期 ${dayKey} 测试失败:`, error.message)
          results.push({ date: dayKey, result: { error: error.message } })
        }

        // 下一天
        currentDate = currentDate.add(1, 'day')
      }

    } catch (error) {
      console.error('测试设置失败:', error)
      throw error
    }
  }, 1E8) // 5分钟超时

  it('获取下一期的每天的 SOP', async () => {
    const chat_id = '7881302790061272_1688856297674847'

    const sops = await Planner.filterTasksByDate(chat_id, new Date(), new Date(Date.now() + 99 * 24 * 60 * 60 * 1000))
    console.log(JSON.stringify(sops, null, 4))
  }, 30000)

  it('should generate plan for a specific day (helper test)', async () => {
    const chat_id = '7881301047907394_1688854546332791'

    // 客户可以修改这里来测试特定的日期
    const testDate = '2025-08-16' // 修改为你想测试的日期

    try {
      const courseStartTime = await DataService.getCourseStartTime(chat_id)
      const currentDate = dayjs(testDate)

      console.log(`\n=== 单独测试日期: ${testDate} ===`)

      // 设置 mock 数据
      const customerPortrait = await prepareCustomerPortrait(chat_id)
      const memories = await prepareMemories(chat_id, currentDate)
      const customerBehavior = prepareCustomerBehavior(1)

      getCustomerPortraitSpy.mockResolvedValue(customerPortrait)
      getRecentMemoriesSpy.mockResolvedValue(memories)
      getCustomerBehaviorSpy.mockResolvedValue(customerBehavior)

      // Mock getCurrentTime
      const mockTime = await taskTimeToCourseTime(
        currentDate.startOf('day').toDate(),
        chat_id
      )
      getCurrentTimeSpy.mockResolvedValue(mockTime)

      // Mock SOP 数据
      const dailySOPMap = prepareDailySOPMap()
      const dailySOP = dailySOPMap[testDate] || []
      filterSOPByDateSpy.mockResolvedValue(dailySOP)

      // 调用 generatePlan
      const result = await BigPlanner.generatePlan(chat_id)

      // 详细输出结果
      console.log('完整结果:', JSON.stringify(result, null, 2))

      expect(result).toBeDefined()
      if (result) {
        expect(result.planResponse).toBeDefined()
        expect(result.context).toBeDefined()
      }

    } catch (error) {
      console.error('单独测试失败:', error)
      throw error
    }
  }, 60000)

})

describe('BigPlanner Tests', function () {
  beforeAll(() => {

  })

  it('Big Planner 测试', async () => {


  }, 30000)

  it('test prompt', async () => {
    const prompt = await getPrompt('test')

    console.log(await LLM.predict(prompt, { model: 'gpt-5-mini', projectName: 'playground' }, { question: { fk: 1 } }))
  }, 30000)

  it('should have plan method', async () => {
    const chat_id = '7881301047907394_1688854546332791'
    // console.log(await BigPlanner.buildContext(chat_id))
    console.log(JSON.stringify(await BigPlanner.generatePlan(chat_id), null, 4))
  }, 1E8)

  it('1231', async () => {
    const prompt = await getPrompt('free-big-plan')
    console.log(prompt)
  }, 30000)

  it('should pass', async () => {
    const promptTemplate = await PromptTemplate.fromTemplate(`# [核心角色定位]
你是一名顶级的训练营销售策略师，专注于通过 1v1 对话引导客户完成认知和信任的跃迁，最终达成“21天系统班”的转化，你需要对当天的任务进行规划

# [核心心智模型：顶级销售的思考流程]
你的所有规划都必须遵循以下三步思考闭环。这是你的第一性原则，绝不偏离。

1.  **客户状态诊断:**
    -   **成交阶段:** 客户位于哪一段？（认知→体验→承诺→成交）
    -   **关系，信任梯:** 客户现在在哪一级？（陌生→熟悉→信任→拥护）
    -   **价值感知:** 他是否从已有的课程/互动中获得了“aha moment”？他感知到的价值是情绪释放、知识获取，还是看到了解决问题的希望？
    -   **核心痛点匹配:** 我们展示的课程价值，是否精准地戳中了他暴露出来的核心痛点（例如：失眠、财富焦虑、行动力不足）？
2.  **今日策略目标:**
    -   基于诊断，明确今天最重要的“状态迁移”目标。这不是“发消息”，而是一个客户认知的转变。
3.  **对话主线设计**

# [基础信息输入]

## 课程节奏安排（必须遵守）
- 上课周前
  - 小讲堂（上课周前）：介绍 3 天课程、普及冥想作用、带练海浪减压、发送《冥想练习指南》
  - 能量测评（小讲堂中讲解）：帮助学员了解能量状态，会1对1文字解读
  - 开营仪式（上课周前，周日 20:00 社群内）：导师介绍、课程内容、上课地点
- 上课周
  - 第一课（周一 20:00~21:45）情绪减压：解析情绪与睡眠问题根源，带练【沉浸式秒睡】完课会赠送《身心对照表》
  - 第二课（周二 20:00~21:15）财富唤醒：解析财富心理障碍，带练【财富果园】课后可针对画面做一对一文字解读；完课会赠送《阿尔法音频》
  - 第三课（周三 20:00~22:20）红鞋子飞跃（核心课）：提升专注力与能量，带练【红鞋子飞跃】；完课会赠送《红鞋子音频》
  - 第四课（周四 20:00，高阶加播课）蓝鹰预演：提高行动力，预演未来（周三22:20前不公开）；完课会赠送《7天冥想会员卡》

## 销售规则与成交规律
- 商品：21天系统班课程，第三天课（周三）21:00后开始销售。
- 关键成交驱动：课程中的“获得感” + 对未来的“憧憬” + 与个人痛点的“强关联”。
- 高潜客户特征：四课全勤、持续反馈感受、主动提问。优先确保其直播到课。
- 禁止行为：第三天课前禁止任何直接销售。上课周前如果已经看完小讲堂，无需过度打扰客户，或者鼓励参与开营仪式。
- 唯一沟通方式：纯文字消息（无法发送语音、图片、链接、表情包，以及接入外部系统）

## 客户情报
- **客户画像:** {{user_slots}}
- **关键记忆/过往互动:**  {{memory}}
- **行为信号:** {{user_behavior}}
- **沉默信号:** {{silent_analyze}}

## 当前时间与阶段
- **当前时间:** {{current_time}}
- **所处阶段:** {{stage}}
- **今日核心事件:** {{today_event}}
- **明日核心事件:** {{tomorrow_event}}

# [今日已有的任务列表]
{{existing_task}}

# [输出指令]
严格按照以下逻辑和格式进行思考和输出。

1.  **[第一步：诊断与策略]** 在 "think" 字段中，先进行深入的诊断分析：
    -   **客户状态诊断:** 运用【核心心智模型】诊断客户当前的成交阶段、信任梯、价值感知、痛点匹配。**必须引用【客户情报】中的具体信息作为论据。**
    -   **核心成交阻力:** 基于诊断，用一句话总结当前最关键的成交阻力。
    -   **今日策略目标:** 定义今天要实现的，客户的“状态迁移”目标。
    -   **对话主线设计:** 规划今天对话的“微型故事”剧情。

2.  **[第二步：任务清单优化]** 接着，在 "think" 字段中，分析【今日已有的任务列表】：
    -   **评估:** 对比你设计的【对话主线】，判断现有任务哪些是匹配的、哪些是冲突的、哪些是冗余的、哪些是缺失的。

3.  **[第三步：生成JSON输出]* 
输出格式为
{
  "think": "(严格按照[输出指令]中的第一步和第二步，展示你的思考过程)",
  "plans": {
    "toAdd": [{
      "content": "(新增的任务内容，必须服务于对话主线)",
      "send_time": "(当天的发送时间，格式如：16:30)"
     }
    ],
    "toUpdate": [
      {
        "id": "（需要更新的任务ID）",
        "content": "（根据你的策略优化后的任务内容）"
      }
    ],
    "toRemove": [
      "（需要移除的任务ID）"
    ]
  }
}`, { templateFormat: 'mustache' })

    const users = [
      // Case 1｜预热期：理性型失眠人群（小叶）
      {
        user_slots: `
- 昵称：小叶（F/28，北京）
- 职业：互联网产品经理（并行项目多）
- 家庭：单身，独居
- 收入：年薪约35万
- 作息/设备：常23:30后入睡；夜醒2–3次；iPhone；微信活跃21:30–00:30
- 目标：7天把入睡时长压到≤15分钟；21天形成稳定晚间仪式
- 核心痛点：入睡困难、脑压高、晨起乏力
- 购买偏好：理性决策，要证据与复利逻辑；预算2–3k，可分期
- 抗拒点：担心"玄学"、怕浪费时间；不爱群内曝光
- 关键词：科学、可操作、效率、数据可见
`,
        memory: `
- 参与小讲堂并做海浪减压8分钟，反馈"头部压力从3降到1，有哈欠"
- 能量测评：情绪电量48/100；睡眠质量2/5；专注度3/5；焦虑7/10
- 过往：用睡眠App打卡14天后放弃；喜欢番茄钟
- 已领取并收藏《冥想练习指南》
- 私聊提问：长期失眠是否影响记忆力？
`,
        user_behavior: `
- 小讲堂准时到课（停留87分钟），提交测评表单
- 群内发言2次；私信1次；22:48完成3分钟呼吸练习打卡
- 互动时段集中在22:30–23:50
`,
        silent_analyze: `
- 18:00–21:00常沉默（通勤/晚饭），不适合长信息
- 对"科学依据/数据对照"类内容回复率高；对"价格/报名通道"暂未回应
- 建议用要点+单图格式，便于快速消化
`,
        current_time: '2025-08-16 19:10',
        stage: '认知（小讲堂当晚）',
        today_event: '小讲堂（上课周前）+能量测评引导',
        tomorrow_event: '开营仪式（周日 20:00 社群）',
        existing_task: `[
      {"id":"A2","content":"发送《冥想练习指南》并标注入门要点","send_time":"2025-08-16 22:00"},
      {"id":"A3","content":"发送能量测评表单","send_time":"2025-08-16 22:30"},
      {"id":"A4","content":"私信1句个性化测评反馈","send_time":"2025-08-16 23:15"},
      {"id":"A7","content":"发布明日20:00开营仪式海报与入群指引","send_time":"2025-08-16 23:45"}
    ]`
      },

      // Case 2｜开营仪式后：ROI导向的创业者（阿城）
      {
        user_slots: `
- 昵称：阿城（M/35，上海）
- 职业：ToB创业者（轻资产服务，现金流压力）
- 家庭：已婚一娃
- 收入：波动大（15–60万/年）
- 目标：缓解财务焦虑，提高决策清晰度
- 核心痛点：入睡易醒、白天心悸；对"回报"高度敏感
- 购买偏好：看案例与ROI；预算3–5k，可月付
- 抗拒点：担心耗时、与业务无关的"心灵课程"
`,
        memory: `
- 错过小讲堂，回放看了17分钟（倍速1.25）
- 开营仪式期间在群里问："课程对赚钱有直接帮助吗？"
- 能量测评：压力8/10；注意力3/5；睡眠3/5
- 曾学过"效率手册"类课程，能坚持2周
`,
        user_behavior: `
- 20:07进入开营直播，停留49分钟；收藏1张"课程路径图"
- 深夜23:40–00:20回复较快；白天响应慢
- 提问集中在"商业应用/ROI/案例"
`,
        silent_analyze: `
- 14:00–19:30在客户拜访，基本不看消息
- 若信息>5行，阅读率下降；偏好时间戳式要点
- 对"成功学口吻"反感，需克制营销语
`,
        current_time: '2025-08-17 21:00',
        stage: '认知→体验（开营仪式后）',
        today_event: '开营仪式（上课周前）',
        tomorrow_event: '第一课（周一 20:00 情绪减压）',
        existing_task: `[
      {"id":"B1","content":"补发小讲堂回放+3个关键片段时间戳","send_time":"2025-08-17 23:45"},
      {"id":"B3","content":"今晚22:00第一课开播提醒","send_time":"2025-08-17 21:30"},
      {"id":"B5","content":"私聊引导完成能量测评并回传截图","send_time":"2025-08-17 23:50"}
    ]`
      },

      // Case 3｜第一课后：新手妈妈（安安）
      {
        user_slots: `
- 昵称：安安（F/31，深圳）
- 身份：休产假进入尾声，新手妈妈
- 目标：改善浅眠与肩颈紧张；不打扰宝宝作息
- 核心痛点：入睡慢、夜醒后难再睡；时间切片化
- 购买偏好：短时高效音频；预算1.5–2k，需分期
- 抗拒点：担心无法连续学习
`,
        memory: `
- 第一课全程；沉浸式秒睡练习10分钟时感到"肩颈热、放松"
- Aha：看到"身心对照表"能自查症状对应练习
- 能量测评：睡眠1/5；情绪电量55/100；专注3/5
- 提及：宝宝夜奶时间大概02:30/05:30
`,
        user_behavior: `
- 课中发送"打哈欠"的表情2次；课后在群里点了"有帮助"
- 晚上23:10以后未读消息（可能忙哄娃）
- 次日10:20补看讲义3页
`,
        silent_analyze: `
- 22:30后易中断；上午10:00–12:00较空
- 喜欢"带练语音+清单式步骤"，不爱长文
- 对"时间友好型方案"敏感
`,
        current_time: '2025-08-18 22:05',
        stage: '体验（第一课后）',
        today_event: '第一课结束；赠《身心对照表》',
        tomorrow_event: '第二课（周二 20:00 财富唤醒）',
        existing_task: `[
      {"id":"C1","content":"课后发送《身心对照表》并标注自查方法","send_time":"2025-08-18 22:15"},
      {"id":"C2","content":"私聊追问"秒睡体验"并收集1句可复用见证","send_time":"2025-08-18 22:45"},
      {"id":"C3","content":"预约明天课前10分钟复训链接","send_time":"2025-08-18 23:00"},
      {"id":"C5","content":"了解家人支持度与可学习时段","send_time":"2025-08-18 23:05"}
    ]`
      },

      // Case 4｜第二课后：学生党（七七）
      {
        user_slots: `
- 昵称：七七（M/24，杭州）
- 身份：考研二战生
- 目标：提升专注与复盘效率
- 核心痛点：拖延、刷短视频、坐不住
- 购买偏好：奖学金/学生价；预算≤1.2k
- 决策链：父母有强影响力
`,
        memory: `
- 第二课全程；"财富果园"画面：土壤湿润但果实稀疏（自评"行动少"）
- Aha：意识到"躲避难题→短期快感"的循环
- 能量测评：注意力2/5；情绪电量62/100；睡眠3/5
`,
        user_behavior: `
- 课后主动交了"果园描述"，点赞6次
- 常在下午16:00–18:00在线；深夜回复慢
`,
        silent_analyze: `
- 对"同龄人案例""学习力提升曲线"感兴趣
- 对价格非常敏感，比较沉默
`,
        current_time: '2025-08-19 22:55',
        stage: '体验（第二课后）',
        today_event: '第二课结束；赠《阿尔法音频》',
        tomorrow_event: '第三课（周三 20:00 红鞋子飞跃—核心课）',
        existing_task: `[
      {"id":"D1","content":"一对一文字解读他的"财富果园"画面","send_time":"2025-08-19 23:10"},
      {"id":"D2","content":"发送《阿尔法音频》并指导午休听10分钟","send_time":"2025-08-19 23:20"},
      {"id":"D3","content":"第三课提醒+价值预告（专注力飞跃）","send_time":"2025-08-19 23:30"},
      {"id":"D4","content":"预留系统班奖学金位","send_time":"2025-08-19 23:00"},
      {"id":"D5","content":"课前试探价格敏感度","send_time":"2025-08-19 23:05"},
      {"id":"D6","content":"群内邀请其简短分享画面感受","send_time":"2025-08-19 23:15"}
    ]`
      },

      // Case 5｜第三课后（可销售期）：高意向的外企中层（Sean）
      {
        user_slots: `
- 昵称：Sean（M/42，广州）
- 职业：外企中层，带8人团队
- 目标：突破"低能量高责任"的瓶颈
- 核心痛点：疲惫与焦虑并存，周末也难休息
- 购买偏好：重视系统性、服务承诺；预算充足
- 抗拒点：担心自己"坚持不下去"
`,
        memory: `
- 第三课后21:30私信："红鞋子飞跃对我很有用"
- Aha：找到了"能量-专注-行动"闭环
- 过往：报过教练课（3个月），中途掉线
- 想要：有人监督的行动样例
`,
        user_behavior: `
- 四课基本全勤趋势；群内发言3次，影响力较高
- 资料查看及时（5分钟内）
`,
        silent_analyze: `
- 抗拒点在"能否坚持"，需提供结构化陪跑与复盘机制
- 晚上22:00后不看消息，第二天9:30前能回复
`,
        current_time: '2025-08-20 22:30',
        stage: '承诺倾向（第三课后，可销售期）',
        today_event: '第三课结束（21:00后开启销售）',
        tomorrow_event: '第四课（周四 20:00 高阶加播：蓝鹰预演）',
        existing_task: `[
      {"id":"E1","content":"邀约1v1问答，澄清目标与卡点","send_time":"2025-08-20 23:00"},
      {"id":"E2","content":"基于第三课练习生成"7天行动样例"","send_time":"2025-08-20 23:15"},
      {"id":"E3","content":"发送系统班介绍长图+学员案例","send_time":"2025-08-20 23:30"},
      {"id":"E4","content":"提供两种支付方案与退费规则说明","send_time":"2025-08-20 23:45"},
      {"id":"E5","content":"预约周四课后跟进","send_time":"2025-08-20 23:50"}
    ]`
      },

      // Case 6｜第三课前（禁止直销）：昼夜颠倒的自由职业者（阿楠）
      {
        user_slots: `
- 昵称：阿楠（M/29，成都）
- 身份：自由职业视觉设计
- 目标：把作息拉回正常，建立交付节律
- 核心痛点：拖延+昼夜颠倒；对"仪式感"有好奇
- 购买偏好：看"时间管理脚本"；预算2k左右
`,
        memory: `
- 第一、二课回放已看70%；常凌晨互动
- 朋友转介绍来的（对导师有初始信任）
- 能量测评：睡眠2/5；注意力3/5；情绪电量50/100
`,
        user_behavior: `
- 常在00:30–02:00活跃；白天不稳定
- 会收藏"工具清单"类内容
`,
        silent_analyze: `
- 对"具体操作脚本"反应最好；对"理念讲解"耐心较低
`,
        current_time: '2025-08-20 18:20',
        stage: '体验（第三课前，禁止直销）',
        today_event: '第三课（20:00–22:20，核心课）',
        tomorrow_event: '第四课预告/第三课回放与答疑',
        existing_task: `[
      {"id":"F1","content":"私信提醒第三课重点与到课福利","send_time":"2025-08-20 18:30"},
      {"id":"F2","content":"引导准备水和本子，营造仪式感","send_time":"2025-08-20 19:40"},
      {"id":"F4","content":"设置今晚"专注模式"","send_time":"2025-08-20 19:50"},
      {"id":"F6","content":"课后收集其"红鞋子飞跃"体验","send_time":"2025-08-20 23:30"}
    ]`
      },

      // Case 7｜第四课后：家庭型决策者（Lydia）
      {
        user_slots: `
- 昵称：Lydia（F/38，南京）
- 职业：小学教师，带两娃
- 目标：修复情绪耗竭，提高下午课堂能量
- 核心痛点：午后崩溃、对孩子发火后自责
- 购买偏好：分期/家庭友好作业；预算2–3k
- 决策链：丈夫为财政把关人，偏保守
`,
        memory: `
- 四课全勤；第二课后反馈"阿尔法音频午休有效"
- 第四课"蓝鹰预演"画面感强，写下未来一周清单
- Aha：意识到"能量跌落点在16:00"
`,
        user_behavior: `
- 群内愿意分享经历；作业完成度高
- 晚上21:30后不看手机；早上6:30–7:30可沟通
`,
        silent_analyze: `
活跃度较高，经常分享家庭生活
`,
        current_time: '2025-08-21 21:50',
        stage: '承诺（第四课后）',
        today_event: '第四课结束；赠《7天冥想会员卡》',
        tomorrow_event: '1v1定制咨询/锁位沟通',
        existing_task: `[
      {"id":"G1","content":"推送《7天冥想会员卡》激活提醒","send_time":"2025-08-21 22:00"},
      {"id":"G2","content":"小窗梳理其"未来预演"脚本并确认时间点","send_time":"2025-08-21 22:15"},
      {"id":"G4","content":"系统班邀约：强调家庭支持与作业轻量化","send_time":"2025-08-21 22:30"},
      {"id":"G5","content":"提供3期/6期分期方案链接","send_time":"2025-08-21 22:45"},
      {"id":"G7","content":"复盘四课中最强的"获得感"，形成个性化承诺","send_time":"2025-08-21 23:00"}
    ]`
      },

      // Case 8｜已成交：程序员新学员的入班运营（Kris）
      {
        user_slots: `
- 昵称：Kris（M/30，武汉）
- 职业：程序员，独居，轻度社交回避
- 目标：21天把注意力块提升到90分钟
- 核心痛点：晚间刷论坛停不下来；自律时好时坏
- 购买偏好：已付费，重视结构化陪跑
`,
        memory: `
- 第三课后即报名；第四课全勤
- 已开始早晨《阿尔法音频》+晚上"秒睡"组合
- 期待：学伴匹配+可视化打卡
`,
        user_behavior: `
- 00:00后基本不在线；早上7:45–8:30在线
- 热衷工具：待办清单App、日历
`,
        silent_analyze: `
- 不喜欢频繁"问候式"私聊；更喜欢明确清单与节奏
- 全程沉默
`,
        current_time: '2025-08-22 10:00',
        stage: '成交（已报名21天系统班）',
        today_event: '入班欢迎+个性化学习计划制定',
        tomorrow_event: '系统班第2天：巩固与回访',
        existing_task: `[
      {"id":"H1","content":"发送欢迎礼包：课表/学习手册/群规","send_time":"2025-08-22 10:15"},
      {"id":"H2","content":"制定个性化21天计划","send_time":"2025-08-22 14:00"},
      {"id":"H3","content":"匹配作息相近的学伴","send_time":"2025-08-22 16:00"},
      {"id":"H4","content":"第3天回访确认练习打卡情况","send_time":"2025-08-22 20:00"}
    ]`
      }
    ]

    const cases = [
      'Case 1：理性型失眠（小讲堂）',
      'Case 2：创业者关注 ROI（开营仪式后）',
      'Case 3：新手妈妈时间碎片（第一课后）',
      'Case 4：学生党预算敏感（第二课后）',
      'Case 5：高意向中层，可销售期（第三课后≥21:00）',
      'Case 6：第三课前，包含“不合规直销”任务用于测试拦截',
      'Case 7：家庭型决策，需要分期与伴侣支持（第四课后）',
      'Case 8：已成交学员，进入入班运营与陪跑场景',
    ]

    const res: any[] = []
    let i = 0
    for (const user of users) {
      const prompt = await promptTemplate.invoke(user)

      const result = await LLM.predict(prompt.value, { model: 'gpt-5-mini', maxTokens: 3000, projectName: 'undefined', responseJSON: true })

      try {
        res.push({
          user: {
            label: cases[i],
            detail: user
          },
          plan: JSON.parse(result)
        })
      } catch (e) {
        console.log(e)
      }

      i++
      console.log(i, user, JSON.stringify(result, null, 2))
    }

    console.log(JSON.stringify(res, null, 4))
  }, 1E8)


  it('测试 pp', async () => {
    await BigPlanner.plan('7881299739985245_1688857003605938')
  }, 1E8)

  it('测试下单人的 大 Planner', async () => {
    Config.setting.localTest = false

    const chats = await DataService.getChatsByCourseNo(81)

    // 过滤出 moer1, meor21 账号下的 chat
    for (const chat of chats) {
      if (!(['1688854340707859', '1688857003605938'].includes(chat.wx_id))) {
        continue
      }

      await BigPlanner.plan(chat.id)
    }
  }, 1E8)

  it('清理任务', async () => {
    const chat_ids = ['7881302942934924_1688854340707859']
    const queue = new Queue<ISchedulePlannerTask>(Planner.getPlannerSOPQueueName('1688854340707859'), {
      connection: RedisDB.getInstance()
    })

    const allJobs = await queue.getDelayed()


    for (const chatId of chat_ids) {
      // const tasks = await TaskManager.getActiveTasks(chatId)

      const plannerSOP = allJobs.filter((item) => item.data.chat_id === chatId)

      console.log(JSON.stringify(plannerSOP, null, 4))
    }
  }, 30000)

  it('simulate planner', async () => {
    // --- 模拟环境 ---

    // 1. 假设这是原始的任务列表 (为了演示，我虚构了内容和时间)
    const originalTasks: TaskItem[] = [
      { id: '1', description: '任务1', time: '09:00', type: 'fixed_time' },
      { id: '2', description: '任务2', time: '10:00', type: 'fixed_time' },
      { id: '3', description: '旧的开课前2小时提醒', time: '18:00', type: 'fixed_time' },
      { id: '4', description: '旧的开课前30分钟提醒', time: '19:30', type: 'fixed_time' },
      { id: '5', description: '任务5', time: '19:50', type: 'fixed_time' },
      { id: '6', description: '旧的课中催课', time: '20:10', type: 'fixed_time' },
      { id: '7', description: '旧的正常化说明', time: '20:15', type: 'fixed_time' },
      { id: '8', description: '旧的完课作业引导', time: '21:00', type: 'fixed_time' },
      { id: '9', description: '旧的完课后回访-到课', time: '21:30', type: 'fixed_time' },
      { id: '10', description: '旧的完课后回访-未到课', time: '21:35', type: 'fixed_time' },
      { id: '11', description: '旧的回放提醒', time: '22:00', type: 'fixed_time' },
    ]

    // 2. 这是您提供的计划操作JSON对象
    const planOperations = {
      'toAdd': [
        {
          'content': '下午定制化铺垫第2课价值+询问到课承诺+减少上课阻力（含课程时长、回放获取、看不见画面正常化）并与其碎片练习习惯对齐，若今晚无法到课则约定回放完成的具体时间与3分钟实操法。',
          'send_time': '15:00',
          'type': 'fixed_time'
        },
        {
          'content': '课后针对未到课或未完课人群的一次性回放引导+微任务：提供第2课3个要点提纲、提醒3分钟实操法、承诺看完回放后私发画面我做一对一解读，确保当晚或次日碎片完成。',
          'send_time': '21:35',
          'type': 'fixed_time'
        }
      ],
      'toUpdate': [
        { 'id': '3', 'content': '开课前2小时定制提醒：重申第2课与门店经营的关联（决策清晰、缓解现金流焦虑、专注力），若无法到场请直接回复“回放”，我为你设置回放+微任务清单。' },
        { 'id': '4', 'content': '开课前30分钟价值铺垫强化：正常化“看不见果园画面也有效”，强调呼吸与身体感受为主，今晚只需放松跟随，回放同样能完成练习。' },
        { 'id': '6', 'content': '课中统一催课与正常化：已开始10分钟，错过开场也能跟上；看不见画面先跟呼吸与身体触点，结束后我一对一解读你记到的任何元素。' },
        { 'id': '8', 'content': '完课作业引导：请用3-5条文字描述你的“财富果园”任意元素（果实/树/门/季节/呼吸流动/是否顺畅）；我将一对一文字解读，给出下一步练习建议。' },
        { 'id': '11', 'content': '当晚回放最后提醒：今晚可用回放完成练习；即使看不见画面也没关系，先做3分钟呼吸+1个你能感受到的意象点，完成后把要点发我，我来一对一解读。' }
      ],
      'toRemove': [],
      'toMerge': [
        {
          'from': ['6', '7'],
          'into': '6',
          'mergedContent': '课中统一催课与正常化：课程已进行中，未进场可立即进入；即便看不见画面，先跟随呼吸与身体感受，抓住老师引导的关键词即可。课后把你记到的任意元素发我，我做一对一解读。'
        },
        {
          'from': ['9', '10'],
          'into': '9',
          'mergedContent': '完课后统一回访：到课同学—请分享你的果园要点，我一对一解读；未到课同学—这里是回放与微任务清单，今晚或明天下午用3-10分钟完成，完成后把要点发我，我来解读并给个性化练习建议。'
        }
      ]
    }

    // 3. 运行模拟器和格式化函数
    const simulationResult = PlanOperationsSimulator.simulateOperations(originalTasks, planOperations)
    const visualOutput = PlanOperationsSimulator.formatUpdatedTasksVisual(simulationResult)

    // 4. 打印最终的可视化结果
    console.log(visualOutput)
  }, 30000)

  it('测试 Planner123123123', async () => {
    console.log(JSON.stringify({
      'user_slots': '{"身份定位":{"职业身份":"","生活角色":"已婚，照顾有伴侣关系"},"痛点":{"表层痛点":"夜间被伴侣酗酒回家后难以再次入睡、睡眠被打断","深层动机":"希望情绪更稳定、能放过自己并快速恢复平静","触发场景":"伴侣夜间喝酒回家引发愤怒与反复醒来"},"认知基础":{"冥想经验":"有基础","相关知识":"理解冥想可带来心安与情绪转换（角度转换）","对老师认知":"了解（对老师讲解成因与方法有认同感）"},"决策特征":{"付费历史":"未知","决策风格":"兼具理性与体验导向（基于效果反馈决定）","核心顾虑":["时间（下班太晚、观看时间受限）","效果（练习是否能持续改善睡眠与情绪）"]},"偏好":{"冥想偏好":"静坐/引导式练习（呼吸、海浪、身体扫描）"},"辅助信息":{"性别":"","宗教信仰":"未知","头像备注":""},"叙事":"客户反馈练习后感到心安放松并能入睡；因伴侣夜间喝酒回家曾多次醒来，听到老师关于“角度转换”后能放过自己并再次入睡；客户表示只能于22:30观看回放并尽量看部分直播；随后报出能量测评分数287。助教安排回放并承诺提醒，客户表现出继续练习的意向。","课程阶段":"入门营-第2课（财富唤醒）参与者","待处理事项":{"登录问题":"","资料发送":""},"信号":{"学习参与度":"有（已练习并计划继续观看回放）","痛点揭露":"有（夜间醒来、伴侣酗酒相关情绪）","价值认同":"有（对老师讲解产生触动并采纳方法）","兴趣点":["情绪转换方法（角度转换）","短时引导练习以助睡眠"],"购买信号":"询问课程安排与系统班（在对话中被助教提及，客户对更系统课程表现出关注）","承诺行为":"计划在22:30观看回放并练习","阻力信号":["时间冲突（下班晚、观看时间受限）"]},"洞察":{"信任度变化":"上升（对老师讲解产生认同和放松效果）","购买意向":"存在兴趣，倾向先体验效果再决定","痛点课程匹配":{"情绪减压":"高度匹配","财富唤醒":"部分相关（助延展安全感到金钱信念）","红靴子专注力和能量提升":"待验证"},"核心阻力":"担心时间无法持续投入以形成习惯"}}',
      'memory': '客户表示通过冥想练习感到醒来后心安放松，并能利用角度转换放过自己，从而再次安心入睡。客户因伴侣夜间喝酒回家而曾感到愤怒与焦虑，但在听到老师关于成因与角度转换的讲解后被触动，认为“转换角度去看这件事儿”让自己放过自己并恢复睡眠。客户计划在晚上22:30观看回放（只能在22:30观看重播），并会尽量在下班后看部分直播；已接受老师安排锁定回放并在22:30前提醒。客户在练习中有入睡时被反复打断的经历（伴侣未回家导致醒来多次），后来通过角度转换再次安心睡着。客户的能量测评分数为287，并已告知该数值给老师。客户表示收到老师的安排和建议，随后去上班。\n客户表示没有时间看直播，询问如何观看回放，并确认了课程页面和录播属于冥想相关内容。客户计划在晚上22:30观看回放，下班太晚可能只能看部分直播，请求老师锁好回放并在22:30前提醒。客户反馈练习后感到醒来时心安放松，认同这是冥想的核心效果。客户对老师讲解人为焦虑、配偶喝酒等成因部分触动较大，听到“转换角度去看这件事儿”后能放过自己，从而再次安心入睡；在练习中，呼吸、海浪或身体扫描段落暂未明确指出最受用的具体段落，计划后续再认真听完整回放以找到重点。\n客户表示计划在睡前练习冥想，并会用沉浸秒睡音频入睡；计划在指定时间（今晚睡前和次日晚20:00在路上先听直播后半段，回家补前半段）安排练习（该时间为客户所述）。客户被建议在明天找碎片时间做小课堂海浪冥想热身，客户未明确接受或拒绝此建议。客户自述对自己和伴侣要求过于苛刻，导致事与愿违，内心堵得慌且没有发泄；在工作中可能对未来感到惊恐；生活中充满担忧，正在调整中。客户反馈已试听一次，感觉很好，并感到醒来时心安放松。客户表示没有时间看直播，询问回放观看方式，并确认了两处回放链接（第一课情绪减压回放链接和小课堂链接）；客户确认只能在22:30观看回放，并因下班太晚会尽量观看部分直播。\n客户已下单金额为1000以上，报名包含职场、小白、情绪&睡眠课程；要求以后由麦子老师对接沟通。客户自报测评分为129分，认为睡眠和情绪都重要，但当前更想先解决睡眠问题；平常习惯在睡前听冥想，计划睡前练习沉浸秒睡音频，并同意在路上或碎片时间听直播或海浪冥想热身。客户表示对自己和伴侣要求过苛，使自己心里堵得慌且没有发泄，工作中可能对未来感到惊恐，生活中充满担忧，正在调整中。客户已听了一遍课程内容，反馈“很好”。在老师提示课程直播进行时，客户回复需要回放并要求锁定回放。\n客户已下单金额1000元以上，分班编号已由麦子老师登记，包含职场、小白、情绪&睡眠课程。客户表示以后希望由麦子老师对接，并认可老师在深夜回复信息。客户目前测得分数为129分，认为处于中下区间并愿意通过冥想练习提升，客户明确更想先改善睡眠，同时认为情绪和睡眠都重要。客户通常在睡前听冥想并计划继续在睡前练习沉浸秒睡音频，同时接受老师建议在通勤时或碎片时间听课程并用回放补齐。客户自述对自己和伴侣要求过苛，内心堵得慌、没有发泄，工作中可能对未来感到惊恐，生活中充满担忧，正在调整中。客户反馈已听了一遍课程并觉得很好。',
      'user_behavior': '课程完成详情:\n- 小课堂海浪冥想: 17%\n- 第一课情绪减压(直播): 7%\n- 第一课情绪减压(录播): 100% 已完成\n\n- 暂无作业记录\n\n能量测评: 287分',
      'silent_analyze': '',
      'current_time': '- 当前时间：2025-09-23 周二 17:14:55，下午 今日课程：第2课财富唤醒，未开始。明日课程：第3课\n- 上课时间：2025-09-22 周一开始到周三每晚20:00\n## 阶段背景\n第一节课已经上完了，今晚开始第二节课财果园。',
      'existing_task': [
        {
          'id': '1',
          'description': 'day2 8点上课通知',
          'time': '2025-09-23 19:55:00'
        },
        {
          'id': '2',
          'description': 'day2第2课财富果园开始10分钟未到课提醒客户到课',
          'time': '2025-09-23 20:10:00'
        },
        {
          'id': '3',
          'description': 'DAY2 第2课财富果园，课程已经开始30分钟，催客户进来上课。',
          'time': '2025-09-23 20:30:24'
        },
        {
          'id': '4',
          'description': 'day2 完课人群发送作业模版财富果园画面',
          'time': '2025-09-23 21:15:00'
        }
      ]
    }, null, 4))
  }, 30000)
})