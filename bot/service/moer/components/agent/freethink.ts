import logger from '../../../../model/logger/logger'
import { ContextBuilder } from './context'
import { EventTracker, IEventType } from '../../../../model/logger/data_driven'
import { ExperienceRecall } from '../../../../model/experience/experience_recall'
import { getPrompt } from './prompt'
import { IWorkflowState } from '../flow/flow'
import { LLM } from '../../../../lib/ai/llm/LLM'
import { MetaActionStage } from '../meta_action/meta_action_router'
import { SalesNodeHelper } from '../flow/helper/salesNodeHelper'
import { TaskManager } from '../planner/task/task_manager'

export class FreeThink {
  public static async invoke(state: IWorkflowState, metaActionStage: MetaActionStage) {
    const freeThinkPrompt = await getPrompt('free-think-moer')
    const ruleLimits = ContextBuilder.getRuleLimits()
    let customerBehavior = await ContextBuilder.getCustomerBehavior(state.chat_id)
    const referenceStrategy = await TaskManager.getStringifyActiveTasks(state.chat_id)
    const customerPortrait = await ContextBuilder.getCustomerPortrait(state.chat_id)
    const dialogHistory = await SalesNodeHelper.getChatHistory(state.chat_id, 6, 18)
    const temporalInformation = await ContextBuilder.temporalInformation(state.chat_id)
    await state.interruptHandler.interruptCheck()

    const experience = await ExperienceRecall.recallScene(state.chat_id, state.round_id)
    if (experience.length > 0) {
      customerBehavior += `\n\n${experience}`
    }

    const output = await LLM.predict(
      freeThinkPrompt, {
        model: 'gpt-5',
        responseJSON: true,
        meta: {
          promptName: 'free_think',
          chat_id: state.chat_id,
          round_id: state.round_id
        } }, {
        thinkPrompt: metaActionStage.thinkPrompt,
        metaActions: metaActionStage.metaActions,
        ruleLimits: ruleLimits,
        customerBehavior: customerBehavior,
        customerPortrait: customerPortrait,
        referenceStrategy: referenceStrategy,
        dialogHistory: dialogHistory,
        temporalInformation: temporalInformation,
      }
    )

    let think: string = ''
    let create: [] = []
    let action: string[] = []
    let strategy: string | string[] = '正常回复'
    let task: string[] = []
    let content: string = ''

    try {
      const parsedOutput = JSON.parse(output)
      think = parsedOutput.think
      create = parsedOutput.create
      action = parsedOutput.action
      strategy = parsedOutput.strategy
      if (Array.isArray(strategy)) {
        task = strategy.slice(2)
        strategy = strategy.slice(0, 2).join(' ')
      }
      content = parsedOutput.content
    } catch (error) {
      logger.error('FreeThink 解析 JSON 失败:', error)
    }

    EventTracker.track(state.chat_id, IEventType.FreeThink, {
      round_id: state.round_id,
      think: think,
      create: JSON.stringify(create),
      action: JSON.stringify(action),
      strategy: strategy,
      task: JSON.stringify(task),
      content: content
    })
    logger.debug({ chat_id: state.chat_id, round_id: state.round_id }, `think: ${think}\naction: ${JSON.stringify(action)}\nstrategy: ${strategy}\ntask: ${JSON.stringify(task)}`)
    return { think, action, strategy, task, content }
  }
}