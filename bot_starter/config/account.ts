import { PrismaMongoClient } from '../../bot/model/mongodb/prisma'

export interface Account {
    orgToken: string
    nickname: string
    wechatId: string
    botUserId: string
    address: string
    port: number

    notifyGroupId: string
    classGroupId: string
    isGroupOwner?: boolean
    proxyGroupNotify?: boolean
    classGroupIds?: string[]
}

interface IMoerEnterpriseConfig {
    notifyGroupId: string
    classGroupId: string
    isGroupOwner?: boolean
    proxyGroupNotify?: boolean
    classGroupIds?: string[]
}

export class ClientAccountConfig {
  private static idServerAddressMap: Map<string, string> = new Map() // wechatId -> serverAddress
  private static wechatNameMap: Map<string, Account> = new Map() // nickname -> account

  private static async pullAllConfig() {
    const configs = await PrismaMongoClient.getConfigInstance().config.findMany()
    for (const config of configs) {
      this.idServerAddressMap.set(config.wechatId, config.address)
    }
  }

  private static async pullEnterpriseConfig() {
    const configs = await PrismaMongoClient.getConfigInstance().config.findMany(
      {
        where: {
          enterpriseName: 'moer'
        }
      }
    )

    for (const config of configs) {
      const enterpriseConfig = config.enterpriseConfig as unknown as IMoerEnterpriseConfig

      this.wechatNameMap.set(config.accountName, {
        orgToken: config.orgToken,
        nickname: config.accountName,
        wechatId: config.wechatId,
        botUserId: config.botUserId,
        address: config.address,
        port: Number(config.port),
        notifyGroupId: enterpriseConfig.notifyGroupId,
        classGroupId: enterpriseConfig.classGroupId,
        isGroupOwner: enterpriseConfig.isGroupOwner,
        proxyGroupNotify: enterpriseConfig.proxyGroupNotify,
        classGroupIds: enterpriseConfig.classGroupIds,
      })
    }
  }

  public static async getServerAddressByWechatId(wechatId: string) {
    // 如果本地没有，从数据库尝试读取
    if (this.idServerAddressMap.has(wechatId)) {
      return this.idServerAddressMap.get(wechatId)
    }

    await this.pullAllConfig()
    return this.idServerAddressMap.get(wechatId)
  }

  public static async getAccountByName(name: string) {
    if (this.wechatNameMap.has(name)) {
      return this.wechatNameMap.get(name)
    }

    await this.pullEnterpriseConfig()
    return this.wechatNameMap.get(name)
  }
}