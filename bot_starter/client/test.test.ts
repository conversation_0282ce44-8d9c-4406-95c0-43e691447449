import { <PERSON><PERSON><PERSON><PERSON> } from './test'
import { sleep } from '../../bot/lib/schedule/schedule'
import { <PERSON><PERSON><PERSON><PERSON> } from '../../bot/lib/date/date'
import { DataService } from '../../bot/service/moer/getter/getData'
import dayjs from 'dayjs'
import { MoerAPI } from '../../bot/model/moer_api/moer'
import { Config } from '../../bot/config/config'
import { Queue } from 'bullmq'
import { RedisDB } from '../../bot/model/redis/redis'
import { DanmuDB } from '../../bot/service/moer/database/danmu'
import { ClassGroupTaskManager } from './class_group'
import { ChatDB } from '../../bot/service/moer/database/chat'
import { PrismaMongoClient } from '../../bot/model/mongodb/prisma'
import { ChatStateStore } from '../../bot/service/moer/storage/chat_state_store'
import { ClassGroupSend } from '../../bot/service/moer/components/flow/schedule/task/classGroupSend'
import { calTaskTime, IScheduleTime } from '../../bot/service/moer/components/schedule/creat_schedule_task'
import { ScheduleTask } from '../../bot/service/moer/components/schedule/schedule'
import { loadConfigByWxId } from '../../test/tools/load_config'
import { GroupSend } from '../../bot/service/moer/components/flow/schedule/task/groupSend'
import { listSOPByChatId } from '../../bot/service/moer/components/flow/schedule/task_starter'
import { FlowTaskType } from '../../bot/service/moer/components/schedule/silent_reask_tasks'

describe('Test', function () {
  beforeAll(() => {

  })

  it('84期 planner 触发时间改为凌晨 6 点', async () => {
    // const chats = await PrismaMongoClient.getInstance().chat.findMany({
    //   where: {
    //     wx_id: { in: ['****************', '****************'] },
    //     course_no: 84
    //   }
    // })

    const accounts: string[] = ['****************', '****************']

    for (const account of accounts) {
      const queue = new Queue(`silent_reask_queue_${account}`, {
        connection: RedisDB.getInstance()
      })

      const jobs = await queue.getDelayed()
      for (const job of jobs) {
        if (job.data.task_name === FlowTaskType.BigPlan) {

          console.log(JSON.stringify(job.data, null, 4))
          // 目标时间点
          const runAt = job.timestamp + (job.delay ?? 0)

          // 计算新的延时 = 目标时间点 - 当前时间
          const newDelay = runAt - Date.now()

          // 如果已经过期，就立刻执行
          if (newDelay < 0) {
            await job.promote()
          } else {
            await job.changeDelay(newDelay)
          }

        }
      }
    }



  }, 30000)

  it('清理一下这一期的 sop', async () => {
    // qiaoqiao, moer21, 83期
    const chats = await PrismaMongoClient.getInstance().chat.findMany({
      where: {
        id: '7881300794909689_****************',
        wx_id: { in: ['****************', '****************'] },
        course_no: 84
      }
    })

    for (const chat of chats) {
      const sops = await listSOPByChatId(chat.id)

      // 分组，找到重复的 sop.name
      const seen = new Set<string>()
      for (const sop of sops) {
        if (seen.has(sop.name)) {
          // 已经存在 → 删除
          console.log(`删除重复 SOP: ${sop.name} (${sop.id})`)
          await sop.remove()
        } else {
          seen.add(sop.name)
        }
      }
    }
  }, 1E8)

  it('12312321asdas', async () => {
    const chatId = '7881300305009303_1688855739679096'
    const userId = '7881300305009303'

    const data = {
      'imContactId': '7881300305009303',
      'name': '金子1',
      'avatar': 'http://wx.qlogo.cn/mmhead/CttmTaYSYkTJs89qgibuTEAzcfq7yiaSNb7eGgR9AmFtScRYAa1dMGeQ/0',
      'createTimestamp': 1745716109000,
      'imInfo': {
        'externalUserId': 'wmXvL2CQAA59LSzEnyXlXfj6CASxYSQA',
        'followUser': {
          'wecomUserId': 'MingXiangZhuJiao-LuoJiaLaoShi'
        }
      },
      'botInfo': {
        'botId': '67a9c5f45d32796ac0f80c86',
        'imBotId': '1688855739679096',
        'name': '麦子9',
        'avatar': 'https://wework.qpic.cn/wwpic3az/448172_QsjvTmrpQuqGSkv_1739011707/0'
      }
    }

    const chat = await ChatDB.getById(chatId)
    if (chat && chat.contact.wx_name !== data.name) {
      await ChatDB.updateContact(chatId, userId, data.name)
    }
  }, 60000)

  it('123123', async () => {
    console.log(DataService.getCurrentWeekCourseNo())
  }, 60000)

  it('queuetask', async () => {
    const queue = new Queue('7881302996931716_1688857949631398', {
      connection: RedisDB.getInstance()
    })

    console.log(JSON.stringify(await queue.getJobs(), null, 4))
  }, 60000)

  it('123123aaa', async () => {
    Config.setting.wechatConfig = await loadConfigByWxId('1688858047620029')
    Config.setting.localTest = false
    await new GroupSend().process({
      'chatId': '7881301413991996_1688858047620029',
      'userId': '7881301413991996',
      'name': '红鞋子辅垫打包分享',
      'scheduleTime': {
        'is_course_week': true,
        'day': 3,
        'time': '16:20:00'
      },
      'sendTime': new Date()
    })
  }, 60000)

  it('获取群任务', async () => {
    Config.setting.wechatConfig = await loadConfigByWxId('1688855694714276')


    const queueName = `1688855694714276_${'R:10933504297161826'.replaceAll(':', '')}`

    // 添加群任务
    const queue = new Queue(queueName, {
      connection: RedisDB.getInstance()
    })

    // 创建对应的 chat 信息
    if (!await ChatDB.getById(queueName)) {
      await PrismaMongoClient.getInstance().chat.create({
        data: {
          id: queueName,
          contact: {
            wx_id: queueName,
            wx_name: '班级群任务',
          },
          chat_state: ChatStateStore.get(queueName),
          course_no: DataService.getCurrentWeekCourseNo(),
          wx_id: Config.setting.wechatConfig?.id as string,
        }
      })
    } else {
      await PrismaMongoClient.getInstance().chat.update({
        where: {
          id: queueName
        },
        data: {
          course_no: DataService.getCurrentWeekCourseNo(),
        }
      })
    }

    // 清空队列，以防重复添加任务
    await queue.obliterate({ force: true })

    // 获取任务
    const tasks = await ClassGroupSend.getTask('fk')
    for (const task of tasks) {
      task.sendTime = await calTaskTime(task.scheduleTime as IScheduleTime, task.chatId, true)
    }

    // 添加任务到队列
    await ScheduleTask.addTasks(queueName, tasks)


    console.log(await queue.getJobs())
    // await queue.obliterate({ force: true })
  }, 60000)

  it('更新群任务处理时间', async () => {
    Config.setting.wechatConfig =   {
      id: '****************',
      botUserId: 'MaiZi',
      name: '暴叔',
      notifyGroupId: 'R:10735753744477170',
      classGroupId: 'R:10841257605945218',
      courseNo: 1
    }

    // 拉取 所有配置，来更新

    // 每周定时启动新的任务队列
    const queue = new Queue(`create_${ClassGroupTaskManager.getQueueName()}`, { connection:  RedisDB.getInstance() })
    const repeatableJobs = await queue.getRepeatableJobs()

    await queue.obliterate({ force: true })

    console.log(JSON.stringify(repeatableJobs, null, 4))

    // 添加重复任务
    await queue.add(
      'weeklyStartGroupTask',
      { timestamp: Date.now() },
      {
        repeat: { pattern: '0 30 20 * * 5' },
        jobId: 'weeklyStartGroupTask', // 固定的 jobId 确保只有一个任务
      }
    )

    console.log(JSON.stringify(await queue.getRepeatableJobs(), null, 4))

  }, 60000)

  it('123', async () => {
    console.log(new Date().getHours() >= 20)
  }, 60000)

  it('wtf', async () => {
    await TestHandler.handleTimeConfig({
      chatId: '7881300846030208_1688854546332791',
      jumpTime: DateHelper.add(new Date(), 7, 'day').toISOString(),
      isStartPush: true
    })

    await sleep(1E8)
  }, 1E8)

  it('拉取上一期的弹幕格式', async () => {
    const course = await DataService.getCourseInfoByCourseNo(63)

    console.log(JSON.stringify(course, null, 4))

    // // 拉取第一天弹幕
    // const firstDayLiveId = course.resource.find((resource) => resource.day === 1)?.liveId as number
    // await DataService.pullLiveDanmuAndStore(firstDayLiveId.toString(), '2024-10-28', '2024-10-28')
    //
    //
    // console.log(JSON.stringify(course, null, 4))
  }, 60000)

  it('客户弹幕', async () => {
    console.log(JSON.stringify(await DataService.getCourseInfoByCourseNo(59), null, 4))
  }, 60000)

  it('asdasd', async () => {
    const formattedDate = dayjs().format('YYYY-MM-DD')
    await DataService.pullLiveDanmuAndStore('5205598', formattedDate, formattedDate, 46, 1)
  }, 60000)

  it('获取弹幕', async () => {
    // const danmus = await DataService.getDanmuByChatId('7881300846030208_1688854546332791')
    // console.log(JSON.stringify(danmus, null, 4))

    const currentCourseNo = DataService.getCurrentWeekCourseNo()
    console.log(currentCourseNo)
  }, 60000)

  it('获取弹幕1', async () => {
    const danmus = await DanmuDB.getDanmusByMoerId('938224')
    console.log(JSON.stringify(danmus, null, 4))
  }, 60000)

  it('获取课程', async () => {
    const courseInfo = await MoerAPI.getCurrentCourseInfo(81)
    console.log(JSON.stringify(courseInfo, null, 4))
  }, 60000)


  it('11', async () => {
    Config.setting.courseInfo = {
      no: 37,
      name: 'hi',
      startTime: new Date(),
      lessons: []
    }

    console.log(JSON.stringify(await DataService.getCourseLink(0, ''), null, 4))
  }, 60000)

  it('clae', async () => {
    const queue = new Queue('7881301241917568_****************', {
      connection: RedisDB.getInstance()
    })

    await queue.obliterate({ force: true })
  }, 60000)

  it('redis_queue_test', async () => {
    const queue = new Queue('7881303525966096_1688857949631398', {
      connection: RedisDB.getInstance()
    })
    console.log(JSON.stringify(await queue.getJobs(), null, 4))
  }, 60000)
})